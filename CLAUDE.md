# Rules

- Use the stgDebug variant to build and test changes
- Always use safe calls for potentially nullable views
- Group all required modifications per file; if multiple lines in the same file need changes, complete them together before requesting a review
- Always run the build after making changes
- Avoid creating unnecessary documents. Explain and confirm with the user first before creating any document, and ensure they agree with the proposed documentation approach
- For View Binding with <include> tags: if binding.viewName.visibility fails, check the layout XML - if it's an <include> tag, use binding.viewName.root.visibility instead
- Always create a file to track the progress of current work. If the file already exists, update it instead.
- use String?.orEmpty() instead of String ?: ""
- For view binding in fragment, do not forget to add _binding = null on onDestroyView()