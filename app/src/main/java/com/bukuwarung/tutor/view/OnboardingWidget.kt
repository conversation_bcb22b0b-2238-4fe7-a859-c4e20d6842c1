package com.bukuwarung.tutor.view


import android.annotation.TargetApi
import android.app.Activity
import android.content.Context
import android.graphics.*
import android.os.Build
import android.os.Handler
import android.text.Html
import android.text.Spanned
import android.util.AttributeSet
import android.util.TypedValue
import android.view.*
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.tutor.MaterialIntroConfiguration
import com.bukuwarung.tutor.animation.AnimationFactory
import com.bukuwarung.tutor.shape.*
import com.bukuwarung.tutor.shape.Rect
import com.bukuwarung.tutor.target.Target
import com.bukuwarung.tutor.target.ViewTarget
import com.bukuwarung.tutor.utils.Constants
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.databinding.WidgetOnboardingBinding

class OnboardingWidget : RelativeLayout {
    interface OnboardingWidgetListener {
        fun onOnboardingDismiss(id: String?, body: String, isFromButton: Boolean = false, isFromCloseButton: Boolean = false, isFromOutside: Boolean = false)
        fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean = false)
    }

    /**
     * Mask color
     */
    private var maskColor = 0

    /**
     * OnboardingWidget will start
     * showing after delayMillis seconds
     * passed
     */
    private var delayMillis: Long = 0

    /**
     * We don't draw OnboardingWidget
     * until isReady field set to true
     */
    private var isReady = false

    /**
     * Show/Dismiss OnboardingWidget
     * with fade in/out animation if
     * this is enabled.
     */
    private var isFadeAnimationEnabled = false

    /**
     * Animation duration
     */
    private var fadeAnimationDuration: Long = 0

    /**
     * targetShape focus on target
     * and clear circle to focus
     */
    private var targetShape: Shape? = null

    /**
     * Focus Type
     */
    private var focusType: Focus? = null

    /**
     * FocusGravity type
     */
    private var focusGravity: FocusGravity? = null

    /**
     * Target View
     */
    private var targetView: Target? = null
    private var targetViewList: List<Target>? = null
    private var resource: Int? = null

    /**
     * Eraser
     */
    private var eraser: Paint? = null

    /**
     * Handler will be used to
     * delay OnboardingWidget
     */
    private var onboardingHandler: Handler? = null

    /**
     * All views will be drawn to
     * this bitmap and canvas then
     * bitmap will be drawn to canvas
     */
    private var bitmap: Bitmap? = null
    private var canvas: Canvas? = null

    /**
     * Circle padding
     */
    private var padding = 0

    /**
     * Layout width/height
     */
    private var layoutWidth = 0
    private var layoutHeight = 0

    /**
     * Dismiss on touch any position
     */
    private var dismissOnTouch = false

    /**
     * Will run `onNextButtonClicked` when target is tapped. Defaults to false
     */
    private var nextOnTargetTap = false


    /**
     * Info dialog text color
     */
    private var colorTextViewInfo = 0

    /**
     * Info dialog will be shown
     * If this value true
     */
    private var isInfoEnabled = false

    /**
     * Save/Retrieve status of OnboardingWidget
     * If Intro is already learnt then don't show
     * it again.
     */
//    private var preferencesManager: PreferencesManager? = null

    /**
     * Check using this Id whether user learned
     * or not.
     */
    private var onboardingId: String? = null

    /**
     * When layout completed, we set this true
     * Otherwise onGlobalLayoutListener stuck on loop.
     */
    private var isLayoutCompleted = false

    /**
     * Notify user when OnboardingWidget is dismissed
     */
    private var listener: OnboardingWidgetListener? = null

    /**
     * Perform click operation to target
     * if this is true
     */
    private var isPerformClick = false

    /**
     * Shape of target
     */
    private var shapeType: ShapeType? = null

    /**
     * Use custom shape
     */
    private var usesCustomShape = false
    private var currentStep = 1
    private var maxSteps = 1
    private var body = ""
    private var sendAnalyticsOnDismiss = false
    private var stepsLocation = Location.TOP

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    private lateinit var layoutInfoBinding: WidgetOnboardingBinding

    private fun init(context: Context) {
        setWillNotDraw(false)
        visibility = INVISIBLE
        /**
         * set default values
         */
        maskColor = Constants.DEFAULT_MASK_COLOR
        delayMillis = Constants.DEFAULT_DELAY_MILLIS
        fadeAnimationDuration = Constants.DEFAULT_FADE_DURATION
        padding = Constants.DEFAULT_TARGET_PADDING
        colorTextViewInfo = Constants.DEFAULT_COLOR_TEXTVIEW_INFO
        focusType = Focus.ALL
        focusGravity = FocusGravity.CENTER
        shapeType = ShapeType.CIRCLE
        isReady = false
        isFadeAnimationEnabled = true
        dismissOnTouch = false
        nextOnTargetTap = false
        isLayoutCompleted = false
        isInfoEnabled = false
        isPerformClick = false
        /**
         * initialize objects
         */
        onboardingHandler = Handler()
        eraser = Paint()
        eraser!!.color = -0x1
        eraser!!.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        eraser!!.flags = Paint.ANTI_ALIAS_FLAG

        layoutInfoBinding = WidgetOnboardingBinding.inflate(LayoutInflater.from(context))
        layoutInfoBinding.textviewInfo.setTextColor(colorTextViewInfo)
        layoutInfoBinding.next.setOnClickListener {
            dismiss(true)
            listener!!.onOnboardingButtonClicked(onboardingId)
        }
        layoutInfoBinding.btnClose.setOnClickListener {
            dismiss(isFromButton = false, isFromCloseButton = true)
        }
        viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                targetShape!!.reCalculateAll()
                if (targetShape != null && targetShape!!.point.y != 0 && !isLayoutCompleted) {
                    if (isInfoEnabled) setInfoLayout()
                    removeOnGlobalLayoutListener(this@OnboardingWidget, this)
                }
            }
        })
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        layoutWidth = measuredWidth
        layoutHeight = measuredHeight
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (!isReady) return
        try {
            if (layoutWidth <= 0 || layoutHeight <= 0) {
                dismiss()
                return
            }
        } catch (ex: Exception) {
            ex.printStackTrace()

            // remove material view directly in case of exception when dismissing
            removeMaterialView()
            return
        }
        if (bitmap == null || canvas == null) {
            if (bitmap != null) bitmap!!.recycle()
            bitmap = Bitmap.createBitmap(layoutWidth, layoutHeight, Bitmap.Config.ARGB_8888)
            this.canvas = Canvas(bitmap!!)
        }
        /**
         * Draw mask
         */
        this.canvas!!.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        this.canvas!!.drawColor(maskColor)
        /**
         * Clear focus area
         */
        if (currentStep >= 1) targetShape!!.draw(this.canvas, eraser, padding)
        canvas!!.drawBitmap(bitmap!!, 0f, 0f, null)
    }

    /**
     * Perform click operation when user
     * touches on target circle.
     *
     * @param event
     * @return
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val xT = event.x
        val yT = event.y
        val isTouchOnFocus = targetShape!!.isTouchOnFocus(xT.toDouble(), yT.toDouble())
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                if (isTouchOnFocus && isPerformClick) {
                    targetView!!.view.isPressed = true
                    targetView!!.view.invalidate()
                }
                return true
            }
            MotionEvent.ACTION_UP -> {
                if (isTouchOnFocus && nextOnTargetTap) {
                    listener!!.onOnboardingButtonClicked(onboardingId, true)
                    dismiss(isFromButton = true)
                } else if (dismissOnTouch) {
                    dismiss(isFromOutside = true)
                }
                if (isTouchOnFocus && isPerformClick) {
                    targetView!!.view.performClick()
                    targetView!!.view.isPressed = true
                    targetView!!.view.invalidate()
                    targetView!!.view.isPressed = false
                    targetView!!.view.invalidate()
                }
                return true
            }
            else -> {
            }
        }
        return super.onTouchEvent(event)
    }

    /**
     * Shows material view with fade in
     * animation
     *
     * @param activity
     */
    fun show(activity: Activity, view:ViewGroup? = null) {
        // TODO : added the flag to hide the tutorial for new user for streak task.
        if (!FeaturePrefManager.getInstance().getOnBoardingTutorialDisabled()) {
            view?.let {
                (it).addView(this)
            }?:run{
                (activity.window.decorView as ViewGroup).addView(this)
            }
            setReady(true)
            onboardingHandler!!.postDelayed({ if (isFadeAnimationEnabled) AnimationFactory.animateFadeIn(this@OnboardingWidget, fadeAnimationDuration) { visibility = VISIBLE } else visibility = VISIBLE }, delayMillis)

        }
    }

    fun dismiss(isFromButton: Boolean = false, isFromCloseButton: Boolean = false, isFromOutside: Boolean = false) {
        listener?.onOnboardingDismiss(onboardingId, body, isFromButton, isFromCloseButton, isFromOutside)
        AnimationFactory.animateFadeOut(this, fadeAnimationDuration) {
            visibility = GONE
            removeMaterialView()
        }
    }

    private fun removeMaterialView() {
        if (parent != null) (parent as ViewGroup).removeView(this)
    }

    /**
     * locate info card view above/below the
     * circle. If circle's Y coordiante is bigger than
     * Y coordinate of root view, then locate cardview
     * above the circle. Otherwise locate below.
     */
    private fun setInfoLayout() {
        onboardingHandler!!.post {
            isLayoutCompleted = true
            if (layoutInfoBinding.infoLayout.parent != null) (layoutInfoBinding.infoLayout.parent as ViewGroup).removeView(
                layoutInfoBinding.infoLayout
            )
            val infoDialogParams = LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT)
            if (targetShape!!.point.y < layoutHeight / 2) {
                layoutInfoBinding.infoLayout.gravity = Gravity.TOP
                infoDialogParams.setMargins(
                        0,
                        targetShape!!.point.y + targetShape!!.height / 2,
                        0,
                        0)
            } else {
                layoutInfoBinding.infoLayout.gravity = Gravity.BOTTOM
                infoDialogParams.setMargins(
                        0,
                        0,
                        0,
                        layoutHeight - (targetShape!!.point.y + targetShape!!.height / 2) + 2 * targetShape!!.height / 2)
            }
            setStepsState()
            if (resource != null) {
                layoutInfoBinding.imageviewIcon.setImageResource(resource!!)
            } else {
                layoutInfoBinding.imageviewIcon.visibility = View.GONE
            }
            layoutInfoBinding.infoLayout.layoutParams = infoDialogParams
            layoutInfoBinding.infoLayout.postInvalidate()
            addView(layoutInfoBinding.infoLayout)
//            if (!isImageViewEnabled) {
//                layoutInfo?.imageview_icon?.visibility = GONE
//            }
            layoutInfoBinding.infoLayout.visibility = VISIBLE
        }
    }

    /**
     * SETTERS
     */
    private fun setMaskColor(maskColor: Int) {
        this.maskColor = maskColor
    }

    private fun setDelay(delayMillis: Int) {
        this.delayMillis = delayMillis.toLong()
    }

    private fun enableFadeAnimation(isFadeAnimationEnabled: Boolean) {
        this.isFadeAnimationEnabled = isFadeAnimationEnabled
    }

    private fun setShapeType(shape: ShapeType) {
        shapeType = shape
    }

    private fun setReady(isReady: Boolean) {
        this.isReady = isReady
    }

    private fun setTarget(target: Target) {
        targetView = target
    }

    private fun setImageResource(res: Int?) {
        resource = res
    }

    private fun setSendAnalyticsOnDismiss(send: Boolean) {
        sendAnalyticsOnDismiss = send
    }

    private fun setStepsLocation(location: Location) {
        stepsLocation = location
        if (location == Location.BOTTOM) {
            layoutInfoBinding.layoutInfoMain.let {
                val constraintSet = ConstraintSet()
                constraintSet.clone(it)
                constraintSet.connect(
                    R.id.cl_steps_container, ConstraintSet.TOP,
                    R.id.next, ConstraintSet.TOP, 0
                )
                constraintSet.connect(
                    R.id.cl_steps_container, ConstraintSet.BOTTOM,
                    R.id.next, ConstraintSet.BOTTOM, 0
                )
                constraintSet.applyTo(it)
            }
        }
    }

    private fun setCloseIconVisibility(hideCloseIcon: Boolean) {
        layoutInfoBinding.btnClose.visibility = (!hideCloseIcon).asVisibility()
    }

    private fun setTargetViewList(target: List<Target>) {
        targetViewList = target
    }

    private fun setFocusType(focusType: Focus) {
        this.focusType = focusType
    }

    private fun setShape(shape: Shape) {
        targetShape = shape
    }

    private fun setPadding(padding: Int) {
        this.padding = padding
    }

    private fun setDismissOnTouch(dismissOnTouch: Boolean) {
        this.dismissOnTouch = dismissOnTouch
    }

    private fun setNextOnTargetTap(nextOnTargetTap: Boolean) {
        this.nextOnTargetTap = nextOnTargetTap
    }

    private fun setFocusGravity(focusGravity: FocusGravity) {
        this.focusGravity = focusGravity
    }

    private fun setColorTextViewInfo(colorTextViewInfo: Int) {
        this.colorTextViewInfo = colorTextViewInfo
        layoutInfoBinding.textviewInfo.setTextColor(this.colorTextViewInfo)
    }

    private fun setTextViewInfo(textview_info: CharSequence) {
        layoutInfoBinding.textviewInfo.text = textview_info
    }

    private fun setTextViewInfoHtml(htmlText: String) {
        this.body = htmlText
        layoutInfoBinding.textviewInfo.text = Html.fromHtml(htmlText)
    }

    private fun setHtmlTextViewInfo(htmlTextViewInfo: Spanned) {
        layoutInfoBinding.textviewInfo.text = htmlTextViewInfo
    }

    private fun setTextViewInfoSize(textViewInfoSize: Int) {
        layoutInfoBinding.textviewInfo.setTextSize(
            TypedValue.COMPLEX_UNIT_SP,
            textViewInfoSize.toFloat()
        )
    }

    private fun enableInfoDialog(isInfoEnabled: Boolean) {
        this.isInfoEnabled = isInfoEnabled
    }

    fun setConfiguration(configuration: MaterialIntroConfiguration?) {
        if (configuration != null) {
            maskColor = configuration.maskColor
            delayMillis = configuration.delayMillis
            isFadeAnimationEnabled = configuration.isFadeAnimationEnabled
            colorTextViewInfo = configuration.colorTextViewInfo
            dismissOnTouch = configuration.isDismissOnTouch
            colorTextViewInfo = configuration.colorTextViewInfo
            focusType = configuration.focusType
            focusGravity = configuration.focusGravity
        }
    }

    private fun setUsageId(onboardingId: String?) {
        this.onboardingId = onboardingId
    }

    fun getUsageId() = this.onboardingId

    private fun setListener(listener: OnboardingWidgetListener) {
        this.listener = listener
    }

    private fun setPerformClick(isPerformClick: Boolean) {
        this.isPerformClick = isPerformClick
    }

    /**
     * Builder Class
     */
    class Builder(private val activity: Activity) {
        private val onboardingWidget: OnboardingWidget = OnboardingWidget(activity)
        private val focusType = Focus.MINIMUM
        fun setMaskColor(maskColor: Int): Builder {
            onboardingWidget.setMaskColor(maskColor)
            return this
        }

        fun setDelayMillis(delayMillis: Int): Builder {
            onboardingWidget.setDelay(delayMillis)
            return this
        }

        fun enableFadeAnimation(isFadeAnimationEnabled: Boolean): Builder {
            onboardingWidget.enableFadeAnimation(isFadeAnimationEnabled)
            return this
        }

        fun setShape(shape: ShapeType): Builder {
            onboardingWidget.setShapeType(shape)
            return this
        }

        fun setImageResource(res: Int?): Builder {
            onboardingWidget.setImageResource(res)
            return this
        }


        fun setSendAnalyticsOnDismiss(send: Boolean): Builder {
            onboardingWidget.setSendAnalyticsOnDismiss(send)
            return this
        }

        fun setStepsLocation(location: Location): Builder {
            onboardingWidget.setStepsLocation(location)
            return this
        }

        fun setCloseIconVisibility(hideCloseIcon: Boolean): Builder {
            onboardingWidget.setCloseIconVisibility(hideCloseIcon)
            return this
        }

        fun setFocusType(focusType: Focus): Builder {
            onboardingWidget.setFocusType(focusType)
            return this
        }

        fun setFocusGravity(focusGravity: FocusGravity): Builder {
            onboardingWidget.setFocusGravity(focusGravity)
            return this
        }

        fun setTarget(view: View?): Builder {
            onboardingWidget.setTarget(ViewTarget(view))
            return this
        }

        fun setTargetPadding(padding: Int): Builder {
            onboardingWidget.setPadding(padding)
            return this
        }

        fun setTextColor(textColor: Int): Builder {
            onboardingWidget.setColorTextViewInfo(textColor)
            return this
        }

        fun setInfoText(infoText: CharSequence): Builder {
            onboardingWidget.enableInfoDialog(true)
            onboardingWidget.setTextViewInfo(infoText)
            return this
        }

        fun setHtmlText(htmlText: Spanned): Builder {
            onboardingWidget.enableInfoDialog(true)
            onboardingWidget.setHtmlTextViewInfo(htmlText)
            return this
        }

        fun setInfoTextHtml(htmlText: String): Builder {
            onboardingWidget.enableInfoDialog(true)
            onboardingWidget.setTextViewInfoHtml(htmlText)
            return this
        }

        fun setHeaderText(infoText: CharSequence): Builder {
            onboardingWidget.setHeaderText(infoText)
            return this
        }

        fun setInfoTextSize(textSize: Int): Builder {
            onboardingWidget.setTextViewInfoSize(textSize)
            return this
        }

        fun dismissOnTouch(dismissOnTouch: Boolean): Builder {
            onboardingWidget.setDismissOnTouch(dismissOnTouch)
            return this
        }

        fun nextOnTargetTap(nextOnTargetTap: Boolean): Builder {
            onboardingWidget.setNextOnTargetTap(nextOnTargetTap)
            return this
        }

        fun setUsageId(onboardingId: String?): Builder {
            onboardingWidget.setUsageId(onboardingId)
            return this
        }

        fun setConfiguration(configuration: MaterialIntroConfiguration?): Builder {
            onboardingWidget.setConfiguration(configuration)
            return this
        }

        fun setListener(listener: OnboardingWidgetListener): Builder {
            onboardingWidget.setListener(listener)
            return this
        }

        fun setCustomShape(shape: Shape): Builder {
            onboardingWidget.usesCustomShape = true
            onboardingWidget.setShape(shape)
            return this
        }

        fun performClick(isPerformClick: Boolean): Builder {
            onboardingWidget.setPerformClick(isPerformClick)
            return this
        }

        fun setButtonText(infoText: CharSequence): Builder {
            onboardingWidget.setButtonText(infoText)
            return this
        }

        fun build(): OnboardingWidget {
            if (onboardingWidget.usesCustomShape) {
                return onboardingWidget
            }

            // no custom shape supplied, build our own
            val shape: Shape
            shape = if (onboardingWidget.shapeType == ShapeType.CIRCLE) {
                Circle(
                        onboardingWidget.targetView,
                        onboardingWidget.focusType,
                        onboardingWidget.focusGravity,
                        onboardingWidget.padding)
            } else if (onboardingWidget.shapeType == ShapeType.ROUND_RECT) {
                RoundedRect(
                        onboardingWidget.targetView,
                        onboardingWidget.focusType,
                        onboardingWidget.focusGravity,
                        onboardingWidget.padding)
            } else if (onboardingWidget.shapeType == ShapeType.RECTANGLE_FULL) {
                Rect(
                        onboardingWidget.targetView,
                        onboardingWidget.focusType,
                        onboardingWidget.focusGravity,
                        onboardingWidget.padding, 0, 0)
            } else {
                Rect(
                        onboardingWidget.targetView,
                        onboardingWidget.focusType,
                        onboardingWidget.focusGravity,
                        onboardingWidget.padding, 50, 4)
            }
            onboardingWidget.setShape(shape)
            return onboardingWidget
        }


        fun setCurrentStep(step: Int): Builder {
            onboardingWidget.setCurrentStep(step)
            return this
        }

        fun setMaxSteps(step: Int): Builder {
            onboardingWidget.setMaxSteps(step)
            return this
        }

    }

    private fun setButtonText(infoText: CharSequence) {
        layoutInfoBinding.next.text = infoText
        if (infoText.isNotBlank()) layoutInfoBinding.next.visibility = View.VISIBLE
    }

    private fun setHeaderText(infoText: CharSequence) {
        layoutInfoBinding.infoHeader.text = infoText
        if (infoText.isEmpty()) layoutInfoBinding.infoHeader.visibility = View.GONE
    }

    private fun setMaxSteps(step: Int) {
        maxSteps = step
    }

    private fun setCurrentStep(step: Int) {
        currentStep = step
    }

    private fun setStepsState() {
        layoutInfoBinding.run {
            if (maxSteps >= 4) setCircleAndLineProperties(4, circle4, line3)
            if (maxSteps >= 3) setCircleAndLineProperties(3, circle3, line2)
            if (maxSteps >= 2) setCircleAndLineProperties(2, circle2, line1)
            if (maxSteps <= 1) infoHeader.visibility = View.VISIBLE
        }
    }

    private fun setCircleAndLineProperties(threshold: Int, circle: ImageView?, line: View?) {
        layoutInfoBinding.circle1.visibility = View.VISIBLE
        val activeCircle = R.drawable.circle_onboarding_progress
        val inactiveCircle = R.drawable.circle_onboarding_progress_default
        val activeColor = ContextCompat.getColor(context, R.color.colorPrimary)
        val inactiveColor = ContextCompat.getColor(context, R.color.black_20)
        circle?.visibility = View.VISIBLE
        line?.visibility = View.VISIBLE
        if (currentStep < threshold) {
            circle?.setImageResource(inactiveCircle)
            line?.setBackgroundColor(inactiveColor)
        } else {
            circle?.setImageResource(activeCircle)
            line?.setBackgroundColor(activeColor)
        }
    }

    companion object {
        @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
        fun removeOnGlobalLayoutListener(v: View, listener: OnGlobalLayoutListener?) {
            if (Build.VERSION.SDK_INT < 16) {
                v.viewTreeObserver.removeGlobalOnLayoutListener(listener)
            } else {
                v.viewTreeObserver.removeOnGlobalLayoutListener(listener)
            }
        }

        // just to support java class who use this. can merge this later when 100% kotlin
        fun createInstance(activity: Activity, listener: OnboardingWidgetListener, id: String? = null, anchor: View,
                           res: Int? = null,
                           headerText: String = "",
                           body: String,
                           buttonText: String = "",
                           focusGravity: FocusGravity,
                           shape: ShapeType,
                           currentStep: Int = 1,
                           maxSteps: Int = 1,
                           sendAnalytics: Boolean = true,
                           isSnackBarClick: Boolean = false
        ): OnboardingWidget {
            return createInstance(activity, listener, id, anchor, res, headerText, body, buttonText, focusGravity, shape, currentStep, maxSteps, sendAnalytics, false, 200, isSnackBarClick)
        }

        fun createInstance(activity: Activity, listener: OnboardingWidgetListener, id: String? = null, anchor: View,
                           res: Int? = null,
                           headerText: String = "",
                           body: String,
                           buttonText: String = "",
                           focusGravity: FocusGravity,
                           shape: ShapeType,
                           currentStep: Int = 1,
                           maxSteps: Int = 1,
                           sendAnalytics: Boolean = true,
                           sendAnalyticsOnDismiss: Boolean = false,
                           delay: Int,
                           isSnackBarClick: Boolean = false,
                           isPerformClick: Boolean = false, view: ViewGroup? = null,
                           stepsLocation: Location = Location.TOP, hideCloseIcon: Boolean = false
        ): OnboardingWidget {
            val builder = Builder(activity)
                    .setButtonText(buttonText)
                    .setHeaderText(headerText)
                    .setMaxSteps(maxSteps)
                    .setUsageId(id)
                    .setCurrentStep(currentStep)
                    .setFocusType(Focus.MINIMUM)
                    .setFocusGravity(focusGravity)
                    .setDelayMillis(delay)
                    .setShape(shape)
                    .dismissOnTouch(true)
                    .nextOnTargetTap(true)
                    .enableFadeAnimation(true)
                    .performClick(isPerformClick)
                    .setInfoTextHtml(body)
                    .setTarget(anchor)
                    .setListener(listener)
                    .setImageResource(res)
                    .setSendAnalyticsOnDismiss(sendAnalyticsOnDismiss)
                    .setStepsLocation(stepsLocation)
                    .setCloseIconVisibility(hideCloseIcon)
                    .build()
            builder.show(activity, view)
            return builder
        }
    }
}
