package com.bukuwarung.ui.component.bottomsheet

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.ItemBottomSheetListBinding
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bumptech.glide.Glide

class BottomsheetListAdapter(
    private var list: ArrayList<BottomSheetDataHolder>,
    private val listener: BottomSheetInterface,
    private val showIcon: Boolean = false
) : RecyclerView.Adapter<BottomsheetListAdapter.MyViewerHolder>() {

    interface BottomSheetInterface {
        fun onItemClickListener(id: String)
    }
    
    private var context: Context? = null

    class MyViewerHolder(val binding: ItemBottomSheetListBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: <PERSON>Group, viewType: Int): MyViewerHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemBottomSheetListBinding.inflate(inflater, parent, false)
        context = binding.root.context
        return MyViewerHolder(binding)
    }

    override fun onBindViewHolder(holder: MyViewerHolder, position: Int) {
        val item = list[position]
        with(holder.binding) {
            if (showIcon && item.icon_image.isNotNullOrEmpty()) {
                imgRowIcon.visibility = View.VISIBLE
                tvIndex.visibility = View.INVISIBLE

                context?.let { safeContext ->
                    Glide.with(safeContext)
                        .load(item.icon_image)
                        .into(imgRowIcon)
                }

                imgRowIcon.setOnClickListener {
                    listener.onItemClickListener(item.id)
                }
            } else {
                imgRowIcon.visibility = View.INVISIBLE
                tvIndex.visibility = View.VISIBLE
                tvIndex.text = (position + 1).toString()
            }
            if (item.left_main_text != null) {
                tvLeftMainText.text = item.left_main_text
            }
            if (item.left_subtext.isNotNullOrEmpty()) {
                tvLeftSubtext.text = item.left_subtext
                tvLeftSubtext.visibility = View.VISIBLE
            } else {
                tvLeftSubtext.visibility = View.GONE
            }
            if (item.right_subtext != null) {
                tvRightSubtext.text = item.right_subtext
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun refreshData(dataList: ArrayList<BottomSheetDataHolder>) {
        this.list = dataList
        notifyDataSetChanged()
    }
}