package com.bukuwarung.dialogs.duedate_selector_dialog

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.bukuwarung.R
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity
import com.bukuwarung.activities.transaction.customer.reminder.PaymentReminderDatePicker
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.databinding.DuedateSelectorBinding
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import java.util.*

class DueDateSelectorBottomSheetDialog : BaseBottomSheetDialogFragment() {
    private var activity: CustomerTransactionActivity? = null
    private var _binding: DuedateSelectorBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = DuedateSelectorBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        this.activity = requireActivity() as CustomerTransactionActivity
        setupView()
    }

    private fun setupView() {
        binding.changeDueDateBtn.setOnClickListener {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put("entryPoint", "customer")
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_COLLECTING_CALENDAR_CREATE_NEW_COLLECTING_DATE, propBuilder)
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_COLLECTING_CALENDAR_CREATE_NEW_COLLECTING_DATE, requireActivity())
            setReminderDateAsText()
            dismiss()
        }

        binding.deleteDueDateBtn.setOnClickListener {
            AppAnalytics.trackEvent("collecting_delete_collecting_date")
            AppAnalytics.trackEvent("customer_detail_reminder_unset")
            activity?.setReminderDate(null)
            dismiss()
        }

        binding.closeBtn.setOnClickListener {
            dismiss()
        }
    }

    @SuppressLint("UseRequireInsteadOfGet")
    fun setReminderDateAsText() {
        val calendar = Calendar.getInstance()
        val prefilledDate = activity!!.currentReminderDate
        if (prefilledDate != null && !prefilledDate.isEmpty()) {
            try {
                calendar.time = DateTimeUtils.convertToDateYYYYMMDD(prefilledDate)
            } catch (ex: Exception) {
                Log.e("PaymentReminderDate", "Exception", ex)
            }
        }
        val y = calendar[Calendar.YEAR]
        val m = calendar[Calendar.MONTH]
        val d = calendar[Calendar.DATE]
        val context: Context = activity!!
        val datePickerDialog = DatePickerDialog(
                context,
                R.style.DatePickerDialogParent,
                PaymentReminderDatePicker(activity),
                y,
                m,
                d
        )
        if (!Utility.isBlank(activity!!.paymentReminderDate.text.toString()) && activity!!.reminderDatePicker != null) {
            val datePicker = activity!!.reminderDatePicker
            val year = datePicker.year
            val month = datePicker.month
            datePicker.background = activity!!.resources.getDrawable(R.drawable.bg_button)
            datePicker.setBackgroundColor(Color.WHITE)
            datePickerDialog.updateDate(year, month, datePicker.dayOfMonth)
        }

        val calendarNow = Calendar.getInstance()
        val datePicker = datePickerDialog.datePicker
        datePickerDialog.setButton(DatePickerDialog.BUTTON_POSITIVE, context.getText(R.string.save), datePickerDialog)
        //subtracting 10sec to avoid validation error for min data, min data cannot me less than or equal to current timestamp
        //problem in using currentTimestamp -> fromDate: Sun Jun 21 01:53:21 GMT+07:00 2020 does not precede toDate: Sun Jun 21 01:53:21 GMT+07:00 2020
        //TODO: remove hacky solution of subtracting time to avoid validation error
        datePicker.minDate = calendarNow.timeInMillis - 10000
        datePickerDialog.show()
    }

    companion object {
        @JvmStatic
        fun show(manager: FragmentManager) {
            DueDateSelectorBottomSheetDialog().show(manager, "duedate-bottom-sheet-dialog")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}