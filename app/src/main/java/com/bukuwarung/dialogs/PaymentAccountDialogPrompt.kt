package com.bukuwarung.dialogs

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.databinding.DialogPaymentProfileBinding
import android.view.LayoutInflater

class PaymentAccountDialogPrompt(context: Context): BaseDialog(context, BaseDialogType.POPUP) {
    private lateinit var binding: DialogPaymentProfileBinding

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        binding = DialogPaymentProfileBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        setUpView()
    }

    private fun setUpView() {
        binding.btnOk.setOnClickListener {
            dismiss()
        }
    }
}