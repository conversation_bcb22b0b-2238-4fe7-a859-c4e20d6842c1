package com.bukuwarung.dialogs.printer

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.DialogEnableBluetoothRequestBinding

/**
 * created by <PERSON><PERSON><PERSON><PERSON> at 19/06/20
 */

class EnableBluetoothDialog(
        context: Context,
        private val entryPoint: String,
        private val onDismiss: (EnableBluetoothDialog) -> Unit,
        private val onCancel: (EnableBluetoothDialog) -> Unit
) : BaseDialog(context) {
    private lateinit var binding: DialogEnableBluetoothRequestBinding
    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels*0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        binding = DialogEnableBluetoothRequestBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        setupView()
    }

    private fun setupView() {
        binding.btnAgree.setOnClickListener {
            val prop = AppAnalytics.PropBuilder().put("entryPoint", entryPoint)
            AppAnalytics.trackEvent("click_button_on_bluetooth_enable_request", prop)
            onDismiss(this)
        }

        setOnCancelListener {
            onCancel(this)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        onCancel(this)
    }
}