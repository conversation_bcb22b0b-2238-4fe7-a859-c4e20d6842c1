package com.bukuwarung.dialogs.printer

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.DialogOpenSetupPrinterBinding

class OpenSetupPrinterDialog(
        context: Context,
        private val callBack: () -> Unit
) : BaseDialog(context) {
    private lateinit var binding: DialogOpenSetupPrinterBinding
    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        super.onCreate(savedInstanceState)

        binding = DialogOpenSetupPrinterBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)

        binding.btnClose.setOnClickListener { dismiss() }
        binding.btnGo.setOnClickListener {
            dismiss()
            callBack()
        }
    }

}