package com.bukuwarung.dialogs.printer

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.DialogEnableBluetoothRequestBinding
import android.view.LayoutInflater

class BluetoothRequestDialog(
    context: Context,
    private val entryPoint: String,
    private val isAgree: (agreeToEnableBT: Boolean) -> Unit
) : BaseDialog(context) {
    private lateinit var binding: DialogEnableBluetoothRequestBinding
    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        super.onCreate(savedInstanceState)
        binding = DialogEnableBluetoothRequestBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        setupView()
    }

    private fun setupView() {
        binding.btnAgree.setOnClickListener {
            val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_BT_ADVANCE_TO_ENABLE, prop)
            isAgree(true)
            dismiss()
        }

        setOnCancelListener {
            isAgree(false)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        isAgree(false)
    }
}