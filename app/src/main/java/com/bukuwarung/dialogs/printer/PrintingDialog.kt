package com.bukuwarung.dialogs.printer

import android.animation.Animator
import android.content.Context
import android.os.Bundle
import android.os.Handler
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.DialogPrintingProcessBinding

class PrintingDialog(context: Context) : BaseDialog(context) {
    private lateinit var binding: DialogPrintingProcessBinding
    private val handler = Handler()
    private val animationListener = object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {}

        override fun onAnimationEnd(animation: Animator) {
            handler.postDelayed({ dismiss() }, 1000)
        }

        override fun onAnimationCancel(animation: Animator) {}

        override fun onAnimationRepeat(animation: Animator) {}
    }

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(false)
        val minWidth = (context.resources.displayMetrics.widthPixels*0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        binding = DialogPrintingProcessBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        setState(LOADING, context.getString(R.string.printing))
    }

    // 0-324 || 418-693 loading
    // 325 - 390 success
    // 693 - 822 fail

    fun setState(state: String, message: String) {
        binding.tvMessage.text = message
        when (state) {
            LOADING -> {
                binding.lottieProgress.setMinAndMaxFrame(0, 324)
            }
            SUCCESS -> {
                binding.lottieProgress.apply {
                    repeatCount = 0
                    setMinAndMaxFrame(325, 390)
                    addAnimatorListener(animationListener)
                }
            }
            FAILED -> {
                binding.lottieProgress.apply {
                    repeatCount = 0
                    setMinAndMaxFrame(693, 822)
                    addAnimatorListener(animationListener)
                }
            }
        }
    }

    companion object {
        const val LOADING = "LOADING"
        const val SUCCESS = "SUCCESS"
        const val FAILED = "FAILED"
    }
}