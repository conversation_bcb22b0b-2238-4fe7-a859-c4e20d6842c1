package com.bukuwarung.dialogs.base

import android.content.Context
import android.os.Bundle
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.databinding.BasePromptDialogBinding
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setDrawableRightListener

open class BasePromptDialog(
        context: Context,
        private val onPromptClicked: ((Boolean) -> Unit),
        private val revertOption: Boolean = false,
        private val onCrossClicked: (() -> Unit)? = null
): BaseDialog(context) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(true)
    }

    override fun getResId(): Int = 0

    private lateinit var binding: BasePromptDialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = BasePromptDialogBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        initViews()
    }

    private fun initViews() {
        binding.btnNo.setOnClickListener {
            if (!revertOption) {
                onPromptClicked(false)
            } else {
                onPromptClicked(true)
            }
            dismiss()
        }
        binding.btnYes.setOnClickListener {
            if (!revertOption) {
                onPromptClicked(true)
            } else {
                onPromptClicked(false)
            }
            dismiss()
        }
        binding.titleText.setDrawableRightListener {
            onCrossClicked?.let { it() }
            dismiss()
        }
    }

    protected fun hideBtn() {
        binding.btnYes.visibility = View.GONE
        binding.btnNo.visibility = View.GONE
    }

    protected fun showCrossBtn() {
        binding.titleText.setDrawable(right = R.drawable.ic_cross_black_80)
    }

    protected fun setTitle(title: String?) {
        binding.titleText.text = title ?: context.resources.getString(R.string.default_placeholder)
    }

    protected fun setContent(content: String?) {
        binding.contentText.text =
            content ?: context.resources.getString(R.string.default_placeholder)
    }

    protected fun setPositiveText(positiveText: String?) {
        if (!revertOption) {
            binding.btnYes.text = positiveText ?: context.resources.getString(R.string.yes)
        } else {
            binding.btnNo.text = positiveText ?: context.resources.getString(R.string.yes)
        }
    }

    protected fun setNegativeText(negativeText: String?) {
        if (!revertOption) {
            binding.btnNo.text = negativeText ?: context.resources.getString(R.string.no)
        } else {
            binding.btnYes.text = negativeText ?: context.resources.getString(R.string.no)
        }
    }

}