package com.bukuwarung.dialogs.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.databinding.BasePromptDialogFragmentBinding

open class BasePromptDialogFragment(
        private val onPromptClicked: ((Boolean) -> Unit),
        private val revertOption: Boolean = false
): BaseDialogFragment() {

    private var _binding: BasePromptDialogFragmentBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = BasePromptDialogFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun initViews() {
        binding.btnNo.setOnClickListener {
            if (!revertOption) {
                onPromptClicked(false)
            } else {
                onPromptClicked(true)
            }
            dismiss()
        }
        binding.btnYes.setOnClickListener {
            if (!revertOption) {
                onPromptClicked(true)
            } else {
                onPromptClicked(false)
            }
            dismiss()
        }
    }

    protected fun setTitle(title: String?) {
        binding.titleText.text =
            title ?: requireContext().resources.getString(R.string.default_placeholder)
    }

    protected fun setContent(content: String?) {
        binding.contentText.text =
            content ?: requireContext().resources.getString(R.string.default_placeholder)
    }

    protected fun setPositiveText(positiveText: String?) {
        if (!revertOption) {
            binding.btnYes.text = positiveText ?: requireContext().resources.getString(R.string.yes)
        } else {
            binding.btnNo.text = positiveText ?: requireContext().resources.getString(R.string.yes)
        }
    }

    protected fun setNegativeText(negativeText: String?) {
        if (!revertOption) {
            binding.btnNo.text = negativeText ?: requireContext().resources.getString(R.string.no)
        } else {
            binding.btnYes.text = negativeText ?: requireContext().resources.getString(R.string.no)
        }
    }

}