package com.bukuwarung.dialogs

import android.content.Context
import android.os.Bundle
import android.text.Spannable
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.LayoutGenericConfirmationDialogBinding

class GenericConfirmationDialog(
        context: Context,
        private val layoutRes: Int,
        private val titleRes: Int,
        private val bodyRes: Int,
        private val btnRightRes: Int,
        private val btnLeftRes: Int,
        private val customBody:Spannable? = null,
        private val rightBtnCallback: (() -> Unit)? = null,
        private val leftBtnCallback: (() -> Unit)? = null
) : BaseDialog(context) {
    private lateinit var binding: LayoutGenericConfirmationDialogBinding
    override fun getResId(): Int = 0

    private constructor(b: Builder) : this(b.context, b.layoutRes, b.titleRes, b.bodyRes, b.btnRightRes, b.btnLeftRes, b.customBody, b.rightBtnCallback, b.leftBtnCallback)

    companion object {
        fun create(context: Context, init: Builder.() -> Unit) = Builder(context, init).build()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding = LayoutGenericConfirmationDialogBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    private fun setupView() {
        binding.title.text = context.getString(titleRes)
        binding.body.text = context.getString(bodyRes)
        binding.btnRight.text = context.getString(btnRightRes)
        binding.btnLeft.text = context.getString(btnLeftRes)

        if (customBody != null) binding.body.text = customBody
        binding.btnRight.setOnClickListener {
            rightBtnCallback?.invoke()
            dismiss()
        }
        binding.btnLeft.setOnClickListener {
            leftBtnCallback?.invoke()
            dismiss()
        }
    }

    class Builder private constructor() {

        constructor(_context: Context, init: Builder.() -> Unit) : this() {
            context { _context }
            layoutRes { R.layout.layout_generic_confirmation_dialog }
            init()
        }

        lateinit var context: Context
        var layoutRes: Int = 0
        var titleRes: Int = 0
        var bodyRes: Int = 0
        var btnRightRes: Int = 0
        var btnLeftRes: Int = 0
        var customBody: Spannable? = null

        var rightBtnCallback: (() -> Unit)? = null
        var leftBtnCallback: (() -> Unit)? = null

        private fun context(init: Builder.() -> Context) = apply { context = init() }
        private fun layoutRes(init: Builder.() -> Int) = apply { layoutRes = init() }
        fun titleRes(init: Builder.() -> Int) = apply { titleRes = init() }
        fun bodyRes(init: Builder.() -> Int) = apply { bodyRes = init() }
        fun btnRightRes(init: Builder.() -> Int) = apply { btnRightRes = init() }
        fun btnLeftRes(init: Builder.() -> Int) = apply { btnLeftRes = init() }
        fun customBodyText(init:Builder.() -> Spannable) = apply { customBody = init() }

        fun build() = GenericConfirmationDialog(this)
    }


}