package com.bukuwarung.dialogs.productselector

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.databinding.DialogEditProductBinding

class EditProductDialog(
        context: Context,
        private val dataHolder: ProductDataHolder,
        private val callBack: (ProductDataHolder) -> Unit
) : BaseDialog(context) {
    private lateinit var binding: DialogEditProductBinding
    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding = DialogEditProductBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)

        setupView()
    }

    private fun setupView() {
        val oldName = dataHolder.productEntity?.name
        binding.etProductName.setText(oldName)
        binding.btnCancel.setOnClickListener { dismiss() }
        binding.btnSave.setOnClickListener {
            val newName = binding.etProductName.text.toString()

            if (oldName != newName && newName.isNotEmpty()) {
                dataHolder.productEntity?.name = newName
                callBack(dataHolder)
            }

            dismiss()
        }
    }
}