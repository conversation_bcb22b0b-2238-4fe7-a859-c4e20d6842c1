package com.bukuwarung.dialogs.productselector.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.ItemProductEntryBinding

internal class ProductSelectionAdapter(
        private var availableProduct: List<ProductEntity>
): DefaultRVAdapter<TransactionItemDto>() {

    override var results: List<TransactionItemDto?>? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    fun addEmptyItem() {
        try {
            val current = this.results?.toMutableList() ?: mutableListOf()

            val item = TransactionItemDto()
            current.add(item)

            this.results = current.toList()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<TransactionItemDto> {
        val binding =
            ItemProductEntryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductSelectionViewHolder(
            binding,
            availableProduct
        )
    }

}