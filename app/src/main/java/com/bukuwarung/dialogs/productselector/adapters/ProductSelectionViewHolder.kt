package com.bukuwarung.dialogs.productselector.adapters

import android.R
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.ArrayAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.ItemProductEntryBinding
import java.lang.Exception

internal class ProductSelectionViewHolder(
    private val binding: ItemProductEntryBinding,
        var availableProducts: List<ProductEntity>
) : DefaultRVViewHolder<TransactionItemDto>(binding.root) {

    private val countET = binding.productCountET
    private val nameET = binding.productNameET

    private var nameTextWatcher: TextWatcher? = null
    private var countTextWatcher: TextWatcher? = null

    override fun bind(item: TransactionItemDto) {
        countET.setText(if (item.quantity > 0) item.quantity.toString() else "")
        nameET.setText(item.productName)
        nameET.clearFocus()

        nameTextWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val newText = s.toString()
                item.productName = newText
            }

            override fun beforeTextChanged(text: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(text: CharSequence?, start: Int, before: Int, count: Int) {}
        }
        nameET.addTextChangedListener(nameTextWatcher)
        setupAutocomplete()

        countTextWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val newText = s.toString()

                try {
                    if (!newText.isBlank()) item.quantity = newText.toDouble()
                    else item.quantity = 0.0
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }

            override fun beforeTextChanged(text: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(text: CharSequence?, start: Int, before: Int, count: Int) {}
        }
        countET.addTextChangedListener(countTextWatcher)

        if (item.productName.isNullOrBlank()) {
            // request focus
            nameET.requestFocus()
        } else {
            // unrequest focus
            nameET.clearFocus()
        }
    }

    private fun setupAutocomplete() {
        val productNames = availableProducts.map { it.name }
        val adapter: ArrayAdapter<String> = ArrayAdapter<String>(
            binding.root.context,
                R.layout.select_dialog_item, productNames)

        nameET.threshold = 3
        nameET.setAdapter(adapter)
    }

    override fun free() {
        nameTextWatcher?.let { nameET.removeTextChangedListener(it) }
        countTextWatcher?.let { countET.removeTextChangedListener(it) }
    }
}
