package com.bukuwarung.dialogs.productselector

import android.content.Context
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.productselector.adapters.ProductSelectionAdapter
import com.bukuwarung.databinding.DialogProductEntryBinding

class ProductSelectorDialog(
        context: Context,
        private var availableProducts: List<ProductEntity>,
        private val items: List<TransactionItemDto>,
        private val onSaveClicked: ((List<TransactionItemDto?>) -> Unit)
): BaseDialog(context) {

    private lateinit var adapter: ProductSelectionAdapter
    private lateinit var binding: DialogProductEntryBinding

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels*0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding = DialogProductEntryBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)

        setupView()
    }

    private fun setupView() {
        adapter = ProductSelectionAdapter(availableProducts)
        adapter.results = items
        adapter.setHasStableIds(false)
        binding.productRv.adapter = adapter
        binding.productRv.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)

        binding.saveProductBtn.setOnClickListener {
            val products = adapter.results
            products?.
                    filter {
                        // only send product with > 0 qty
                        it?.productName != null && it.productName.isNotBlank() && it.quantity > 0
                    }?.
                    let {
                onSaveClicked(it)
            }
            dismiss()
        }

        binding.addProductBtn.setOnClickListener {
            adapter.addEmptyItem()
        }
    }

}