package com.bukuwarung.dialogs.payment

import android.animation.Animator
import android.content.Context
import android.os.Bundle
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.databinding.DialogPaymentFinishedBinding
import com.bukuwarung.dialogs.base.BaseDialog

class PaymentFinishedDialog(
        context: Context,
        private val amount: String?,
        private val title: String,
        private val subtitle: String?
) : BaseDialog(context) {
    private lateinit var binding: DialogPaymentFinishedBinding
    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding = DialogPaymentFinishedBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)

        setupView()
    }

    private fun setupView() {
        if (amount != null) binding.amountTxt.text = amount
        else binding.amountTxt.visibility = View.GONE
        binding.titleTxt.text = title
        if (subtitle != null) binding.subtitleTxt.text = subtitle
        else binding.subtitleTxt.visibility = View.GONE
        binding.animation.addAnimatorListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
            }

            override fun onAnimationEnd(p0: Animator) {
                dismiss()
            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }
        })
        binding.animation.playAnimation()
    }
}
