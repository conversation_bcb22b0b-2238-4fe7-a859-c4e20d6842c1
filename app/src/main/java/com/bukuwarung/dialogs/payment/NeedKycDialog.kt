package com.bukuwarung.dialogs.payment

import android.content.Context
import android.os.Bundle
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.databinding.DialogNeedKycBinding

class NeedKycDialog(context: Context, private val isRejected: Boolean? = false) : BaseDialog(context) {
    private lateinit var binding: DialogNeedKycBinding
    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding = DialogNeedKycBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)

        window?.setBackgroundDrawableResource(R.drawable.base_dialog_rounded_20dp)
    }

    private fun setupView() {
        binding.verifyBtn.setOnClickListener {
            val url = "${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}&entryPoint=${AnalyticsConst.ACCOUNTING}"
            context.startActivity(
                    WebviewActivity.createIntent(context, url, "", true)
            )
            dismiss()
        }
        binding.cancelBtn.setOnClickListener {
            dismiss()
        }
        if (isRejected == true) {
            binding.verifyBtn.text = context.getString(R.string.reverify)
            binding.titleTxt.text = context.getString(R.string.verification_failed)
            binding.subtitleTxt.text = context.getString(R.string.verification_failed_message)
        }
    }
}
