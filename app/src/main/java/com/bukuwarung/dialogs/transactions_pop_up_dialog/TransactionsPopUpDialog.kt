package com.bukuwarung.dialogs.transactions_pop_up_dialog

import android.content.Context
import android.graphics.Outline
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.databinding.TransactionsPopUpDialogBinding
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.Utility

class TransactionsPopUpDialog(context: Context, private val action: () -> Unit,
                              private val onDismissAction: () -> Unit,
                              val image:Int, val headText:String, val bodyText:String, val buttonText:String, val imageUrl:String=""): BaseDialog(context, BaseDialogType.POPUP) {

    var transactionsImage = 0
    var transactionsHeadText = ""
    var transactionsBodyText = ""
    var transactionsButtonText = ""

    init {
        transactionsImage = image
        transactionsHeadText = headText
        transactionsBodyText = bodyText
        transactionsButtonText = buttonText
        setCancellable(false)
        setUseFullWidth(false)
    }

    private lateinit var binding: TransactionsPopUpDialogBinding

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = TransactionsPopUpDialogBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    private fun setupView() {
        this.window?.setBackgroundDrawable(ColorDrawable(android.graphics.Color.TRANSPARENT))
        if(!Utility.isBlank(imageUrl)){
            ImageUtils.setImageFromUrl(
                context,
                imageUrl,
                transactionsImage,
                binding.transactionsImage
            )
        }else{
            binding.transactionsImage.setImageResource(transactionsImage)
        }
        binding.closeDialog.bringToFront()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val provider: ViewOutlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight() + 40, 40f)
                }
            }
            binding.transactionsImage.setOutlineProvider(provider)
            binding.transactionsImage.setClipToOutline(true)
        }
        if(transactionsHeadText == "")
            binding.transactionsHeading.visibility = View.GONE
        else
            binding.transactionsHeading.text = transactionsHeadText
        binding.transactionsBody.text = transactionsBodyText
        binding.button.text = transactionsButtonText
        binding.button.setOnClickListener {
            action()
            dismiss()
        }
        binding.closeDialog.setOnClickListener {
            onDismissAction()
            dismiss()
        }
    }
}