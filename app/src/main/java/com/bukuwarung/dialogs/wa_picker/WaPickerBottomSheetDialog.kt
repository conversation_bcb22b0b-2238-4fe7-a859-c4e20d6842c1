package com.bukuwarung.dialogs.wa_picker

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import androidx.lifecycle.Lifecycle
import androidx.viewbinding.ViewBinding
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseBottomSheetDialog
import com.bukuwarung.databinding.WaPickerOtpBinding

class WaPickerBottomSheetDialog(context: Context) : BaseBottomSheetDialog(context) {
    private var url: String? = null
    private var intent: Intent? = null
    private val binding: WaPickerOtpBinding by lazy {
        WaPickerOtpBinding.inflate(LayoutInflater.from(context))
    }

    constructor(context: Context, url: String) : this(context) {
        this.url = url
    }

    constructor(context: Context, intent: Intent) : this(context) {
        this.intent = intent
    }

    override fun getResId(): Int = 0
    override fun getBinding(): ViewBinding? = binding

    init {
        setUseFullWidth(true)
        setCancellable(true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupView()
    }

    private fun setupView() {
        binding.waBtn.setOnClickListener {
            try {
                if (intent == null) {
                    intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                }
                intent?.setPackage("com.whatsapp")
                dismiss()
                context.startActivity(intent)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }

        binding.wa4bBtn.setOnClickListener {
            try {
                if (intent == null) {
                    intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                }
                intent?.setPackage("com.whatsapp.w4b")
                dismiss()
                context.startActivity(intent)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

}