package com.bukuwarung.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.appcompat.content.res.AppCompatResources
import com.bukuwarung.R
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyAccount
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTier
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.api.model.ConfigImages
import com.bukuwarung.api.model.LoyaltyTierImages
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.LayoutLoyaltyAndReferralBinding
import com.bukuwarung.databinding.LayoutUserTierHomepageBinding
import com.bukuwarung.databinding.LayoutUserTierHomepageNewBinding
import com.bukuwarung.payments.data.model.ReferralDataResponse
import com.bukuwarung.payments.data.model.SaldoResponse
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.utils.*

class LoyaltyHomeWidgetWithSaldoBonus @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    private val binding = LayoutUserTierHomepageBinding.inflate(LayoutInflater.from(context), this, true)
    private val newBinding = LayoutUserTierHomepageNewBinding.inflate(LayoutInflater.from(context), this, true)
    private val referralBinding = LayoutLoyaltyAndReferralBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            binding.slLoyalty.startShimmer()
            binding.slLoyalty.showView()
            binding.cvLoyaltyPoints.hideView()
            binding.cvLoyaltyTierItem.hideView()
            referralBinding.layoutLoyaltyShimmer.slReferral.hideShimmer()
            referralBinding.layoutLoyaltyShimmer.slReferral.hideView()
            referralBinding.layoutReferralShimmer.slReferral.hideShimmer()
            referralBinding.layoutReferralShimmer.slReferral.hideView()
            referralBinding.cvShimmerLoyaltyReferral.hideView()
            referralBinding.cvLoyaltyReferral.hideView()
            newBinding.cvNewShimmer.hideView()
            newBinding.slLoyalty.hideView()
            newBinding.cvLoyaltyPoints.hideView()
            binding.layoutPoints.apply {
                ivIcon.loadImage(R.drawable.ic_buku_point)
            }
        } else if (RemoteConfigUtils.getLoyaltyWidgetType() == 1 || (RemoteConfigUtils.getLoyaltyWidgetType() == 2 && !RemoteConfigUtils.showReferralEntryPointHome())) {
            newBinding.slLoyalty.startShimmer()
            newBinding.slLoyalty.showView()
            newBinding.cvLoyaltyPoints.hideView()
            binding.slLoyalty.hideView()
            binding.cvLoyaltyPoints.hideView()
            binding.cvLoyaltyTierItem.hideView()
            referralBinding.layoutLoyaltyShimmer.slReferral.hideShimmer()
            referralBinding.layoutLoyaltyShimmer.slReferral.hideView()
            referralBinding.layoutReferralShimmer.slReferral.hideShimmer()
            referralBinding.layoutReferralShimmer.slReferral.hideView()
            referralBinding.cvShimmerLoyaltyReferral.hideView()
            referralBinding.cvLoyaltyReferral.hideView()
            newBinding.layoutPoints.apply {
                ivIcon.loadImage(R.drawable.ic_buku_point)
            }
        } else {
                newBinding.cvLoyaltyPoints.hideView()
                newBinding.slLoyalty.hideView()
                newBinding.cvNewShimmer.hideView()
                binding.slLoyalty.hideView()
                binding.cvLoyaltyPoints.hideView()
                binding.cvLoyaltyTierItem.hideView()
                referralBinding.layoutLoyaltyShimmer.slReferral.startShimmer()
                referralBinding.layoutLoyaltyShimmer.slReferral.showView()
                referralBinding.layoutReferralShimmer.slReferral.startShimmer()
                referralBinding.layoutReferralShimmer.slReferral.showView()
                referralBinding.layoutLoyalty.root.hideView()
                referralBinding.layoutReferral.root.hideView()
                referralBinding.cvLoyaltyReferral.hideView()
            }
        }

    fun setPoint(saldoBonusResponse : SaldoResponse) {
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            newBinding.cvNewShimmer.hideView()
            binding.slLoyalty.stopShimmer()
            binding.slLoyalty.hideView()
            binding.cvLoyaltyPoints.showView()
            binding.cvLoyaltyTierItem.showView()
            binding.layoutPoints.tvTitle.text =
                    Utility.formatCurrency(saldoBonusResponse.subBalance?.cashback)
            binding.layoutPoints.tvSubTitle.text = context.getString(R.string.point_sub_title)
        } else if (RemoteConfigUtils.getLoyaltyWidgetType() == 1 || (RemoteConfigUtils.getLoyaltyWidgetType() == 2 && !RemoteConfigUtils.showReferralEntryPointHome())) {
            newBinding.slLoyalty.stopShimmer()
            newBinding.slLoyalty.hideView()
            newBinding.cvLoyaltyPoints.showView()
            newBinding.layoutPoints.ivIcon.setImageDrawable(
                AppCompatResources.getDrawable(
                    newBinding.layoutPoints.ivIcon.context,
                    R.drawable.ic_saldo_icon
                )
            )
            newBinding.layoutPoints.tvTitle.text = context.getString(R.string.filter_saldo_bonus)
            newBinding.layoutPoints.tvSubTitle.text = Utility.formatAmount(saldoBonusResponse.subBalance?.cashback)
        } else {
            newBinding.cvNewShimmer.hideView()
            newBinding.cvLoyaltyPoints.hideView()
            binding.root.hideView()
            referralBinding.layoutLoyaltyShimmer.slReferral.stopShimmer()
            referralBinding.layoutLoyaltyShimmer.slReferral.hideShimmer()
            referralBinding.layoutLoyaltyShimmer.slReferral.hideView()
            referralBinding.layoutLoyalty.root.showView()
            referralBinding.layoutLoyalty.ivIcon.setImageDrawable(
                AppCompatResources.getDrawable(
                    newBinding.layoutPoints.ivIcon.context,
                    R.drawable.ic_saldo_membership
                )
            )
            referralBinding.layoutLoyalty.tvTitle.text = context.getString(R.string.filter_saldo_bonus)
            referralBinding.layoutLoyalty.tvSubTitle.text = Utility.formatAmount(saldoBonusResponse.subBalance?.cashback)
        }
        hideShimmerForReferralBinding()
    }

    fun updateReferralData(referralDataResponse: ReferralDataResponse) {
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 2 && RemoteConfigUtils.showReferralEntryPointHome()) {
            referralBinding.layoutReferralShimmer.slReferral.stopShimmer()
            referralBinding.layoutReferralShimmer.slReferral.hideShimmer()
            referralBinding.layoutReferralShimmer.slReferral.hideView()
            referralBinding.layoutReferral.root.showView()
            val count = referralDataResponse.data?.size.orNil
            referralBinding.layoutReferral.tvTitle.text = referralBinding.layoutLoyalty.tvSubTitle.context.getString(R.string.referral_program)
            if (count == 0) {
                referralBinding.layoutReferral.tvSubTitle.text =
                    referralBinding.layoutReferral.tvSubTitle.context.getString(R.string.start_inviting_friends)
            } else {
                referralBinding.layoutReferral.tvSubTitle.text =
                    count.toString().plus(" ")
                        .plus(referralBinding.layoutReferral.tvSubTitle.context.getString(R.string.invited_friends))
            }
            referralBinding.layoutReferral.ivIcon.setImageDrawable(
                AppCompatResources.getDrawable(
                    newBinding.layoutPoints.ivIcon.context,
                    R.drawable.ic_saldo_icon
                )
            )
        }
        hideShimmerForReferralBinding()
    }

    private fun hideShimmerForReferralBinding() {
        with(referralBinding) {
            if (!layoutLoyaltyShimmer.slReferral.isShimmerVisible && !layoutReferralShimmer.slReferral.isShimmerVisible) {
                referralBinding.cvLoyaltyReferral.showView()
            }
        }
    }

    fun setTier(loyaltyTier: LoyaltyTier) {
        SessionManager.getInstance().setLoyaltyTier(loyaltyTier.tierName)
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            binding.slLoyalty.stopShimmer()
            binding.slLoyalty.hideView()
            binding.cvLoyaltyPoints.showView()
            binding.cvLoyaltyTierItem.showView()
            binding.layoutMembershipStatus.apply {
                tvSubTitle.text = context.getString(R.string.tier_sub_title)

                val icon: Pair<String, Int> =
                        if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.BRONZE.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.SILVER.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.silver_tier_icon_url,
                                    R.drawable.ic_tier_silver
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.GOLD.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.gold_tier_icon_url,
                                    R.drawable.ic_tier_gold
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.PLATINUM.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.platinum_tier_icon_url,
                                    R.drawable.ic_tier_platinum
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.DIAMOND.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.diamond_tier_icon_url,
                                    R.drawable.ic_tier_diamond
                            )
                        } else {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        }
                if (loyaltyTier.tierName.contains("Juragan")) {
                    tvTitle.text =
                            loyaltyTier.tierName + "\n" + loyaltyTier?.description?.capitalize()
                                    ?: ""
                } else {
                    tvTitle.text =
                            "Juragan " + loyaltyTier.tierName + "\n" + loyaltyTier?.description?.capitalize()
                                    ?: ""
                }
                ivIcon.loadImage(icon.first, icon.second)
            }
        } else {
            newBinding.slLoyalty.stopShimmer()
            newBinding.slLoyalty.hideView()
            newBinding.cvLoyaltyPoints.showView()
            newBinding.layoutMembershipStatus.apply {
                tvTitle.text = context.getString(R.string.tier_title)
            val icon: Pair<String, Int> =
                        if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.BRONZE.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.SILVER.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.silver_tier_icon_url,
                                    R.drawable.ic_tier_silver
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.GOLD.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.gold_tier_icon_url,
                                    R.drawable.ic_tier_gold
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.PLATINUM.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.platinum_tier_icon_url,
                                    R.drawable.ic_tier_platinum
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.DIAMOND.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.diamond_tier_icon_url,
                                    R.drawable.ic_tier_diamond
                            )
                        } else {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        }
                if (loyaltyTier.tierName.contains("Juragan")) {
                    tvSubTitle.text =
                            loyaltyTier.tierName ?: ""
                } else {
                    tvSubTitle.text =
                            ("Juragan " + loyaltyTier.tierName) ?: ""
                }
                ivIcon.loadImage(icon.first, icon.second)
            }
        }
    }

    fun setTierClickListener(onClick: () -> Unit) {
        binding.layoutMembershipStatus.container.setOnClickListener { onClick() }
    }

    fun setOnPointClickListener(onClick: () -> Unit) {
        binding.layoutPoints.container.setOnClickListener { onClick() }
    }

    fun setOnWidgetClickListener(onClick: () -> Unit) {
        newBinding.vwHome.setOnClickListener { onClick() }
    }

    fun setOnSaldoBonusClickListener(onClick: () -> Unit) {
        referralBinding.layoutLoyalty.root.setOnClickListener {
           onClick()
        }
    }

    fun setOnReferralClickListerner(onClick: () -> Unit) {
        referralBinding.layoutReferral.root.setOnClickListener {
            onClick()
        }
    }
}