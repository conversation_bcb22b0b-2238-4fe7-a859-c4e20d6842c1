package com.bukuwarung.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyAccount
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTier
import com.bukuwarung.api.model.ConfigImages
import com.bukuwarung.api.model.LoyaltyTierImages
import com.bukuwarung.databinding.LayoutUserTierHomepageBinding
import com.bukuwarung.databinding.LayoutUserTierHomepageNewBinding
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.utils.*

class LoyaltyHomeWidget @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    private val binding = LayoutUserTierHomepageBinding.inflate(LayoutInflater.from(context), this, true)
    private val newBinding = LayoutUserTierHomepageNewBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            binding.slLoyalty.startShimmer()
            binding.slLoyalty.showView()
            binding.cvLoyaltyPoints.hideView()
            binding.cvLoyaltyTierItem.hideView()
            newBinding.slLoyalty.hideView()
            newBinding.cvLoyaltyPoints.hideView()
            binding.layoutPoints.apply {
                ivIcon.loadImage(R.drawable.ic_buku_point)
            }
        } else {
            newBinding.slLoyalty.startShimmer()
            newBinding.slLoyalty.showView()
            newBinding.cvLoyaltyPoints.hideView()
            binding.slLoyalty.hideView()
            binding.cvLoyaltyPoints.hideView()
            binding.cvLoyaltyTierItem.hideView()
            newBinding.layoutPoints.apply {
                ivIcon.loadImage(R.drawable.ic_buku_point)
            }
        }
    }

    fun setPoint(loyaltyAccount: LoyaltyAccount) {
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            binding.slLoyalty.stopShimmer()
            binding.slLoyalty.hideView()
            binding.cvLoyaltyPoints.showView()
            binding.cvLoyaltyTierItem.showView()
            binding.layoutPoints.tvTitle.text =
                    Utility.formatCurrency(loyaltyAccount.activePoints.toDouble())
            binding.layoutPoints.tvSubTitle.text = context.getString(R.string.point_sub_title)
        } else {
            newBinding.slLoyalty.stopShimmer()
            newBinding.slLoyalty.hideView()
            newBinding.cvLoyaltyPoints.showView()
            newBinding.layoutPoints.tvTitle.text =
                    Utility.formatCurrency(loyaltyAccount.activePoints.toDouble())
            newBinding.layoutPoints.tvSubTitle.hideView()
        }
    }

    fun setTier(loyaltyTier: LoyaltyTier) {
        if (RemoteConfigUtils.getLoyaltyWidgetType() == 0) {
            binding.slLoyalty.stopShimmer()
            binding.slLoyalty.hideView()
            binding.cvLoyaltyPoints.showView()
            binding.cvLoyaltyTierItem.showView()
            binding.layoutMembershipStatus.apply {
                tvSubTitle.text = context.getString(R.string.tier_sub_title)

                val icon: Pair<String, Int> =
                        if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.BRONZE.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.SILVER.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.silver_tier_icon_url,
                                    R.drawable.ic_tier_silver
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.GOLD.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.gold_tier_icon_url,
                                    R.drawable.ic_tier_gold
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.PLATINUM.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.platinum_tier_icon_url,
                                    R.drawable.ic_tier_platinum
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.DIAMOND.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.diamond_tier_icon_url,
                                    R.drawable.ic_tier_diamond
                            )
                        } else {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        }
                if (loyaltyTier.tierName.contains("Juragan")) {
                    tvTitle.text =
                            loyaltyTier.tierName + "\n" + loyaltyTier?.description?.capitalize()
                                    ?: ""
                } else {
                    tvTitle.text =
                            "Juragan " + loyaltyTier.tierName + "\n" + loyaltyTier?.description?.capitalize()
                                    ?: ""
                }
                ivIcon.loadImage(icon.first, icon.second)
            }
        } else {
            newBinding.slLoyalty.stopShimmer()
            newBinding.slLoyalty.hideView()
            newBinding.cvLoyaltyPoints.showView()
            newBinding.layoutMembershipStatus.apply {
                tvTitle.text = context.getString(R.string.tier_title)
            val icon: Pair<String, Int> =
                        if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.BRONZE.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.SILVER.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.silver_tier_icon_url,
                                    R.drawable.ic_tier_silver
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.GOLD.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.gold_tier_icon_url,
                                    R.drawable.ic_tier_gold
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.PLATINUM.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.platinum_tier_icon_url,
                                    R.drawable.ic_tier_platinum
                            )
                        } else if (loyaltyTier.tierName.toLowerCase().contains(com.bukuwarung.enums.LoyaltyTier.DIAMOND.value)) {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.diamond_tier_icon_url,
                                    R.drawable.ic_tier_diamond
                            )
                        } else {
                            Pair(
                                    RemoteConfigUtils.getLoyaltyTierImages().loyalty.bronze_tier_icon_url,
                                    R.drawable.ic_tier_bronze
                            )
                        }
                if (loyaltyTier.tierName.contains("Juragan")) {
                    tvSubTitle.text =
                            loyaltyTier.tierName ?: ""
                } else {
                    tvSubTitle.text =
                            ("Juragan " + loyaltyTier.tierName) ?: ""
                }
                ivIcon.loadImage(icon.first, icon.second)
            }
        }
    }

    fun setTierClickListener(onClick: () -> Unit) {
        binding.layoutMembershipStatus.container.setOnClickListener { onClick() }
    }

    fun setOnPointClickListener(onClick: () -> Unit) {
        binding.layoutPoints.container.setOnClickListener { onClick() }
    }

    fun setOnWidgetClickListener(onClick: () -> Unit) {
        newBinding.vwHome.setOnClickListener { onClick() }
    }
}