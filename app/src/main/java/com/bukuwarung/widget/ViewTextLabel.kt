package com.bukuwarung.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.databinding.ViewTextLabelBinding
import android.widget.FrameLayout

class ViewTextLabel @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private lateinit var binding: ViewTextLabelBinding

    init {
        binding = ViewTextLabelBinding.inflate(LayoutInflater.from(context), this, true)
        initView(attrs, context)
    }

    private fun initView(attrs: AttributeSet?, context: Context) {
        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.ViewTextLabel, 0, 0)
            val text = typedArray.getString(R.styleable.ViewTextLabel_labelText) ?: ""
            val textColor = ContextCompat.getColor(context, typedArray.getResourceId(R.styleable.ViewTextLabel_labelTextColor, ContextCompat.getColor(context, R.color.white)))
            val labelColor = ContextCompat.getColor(context, typedArray.getResourceId(R.styleable.ViewTextLabel_labelColor, ContextCompat.getColor(context, R.color.out_red)))


            binding.labelText.text = text
            binding.labelText.setTextColor(textColor)
            binding.labelCard.setCardBackgroundColor(labelColor)

            typedArray.recycle()
        }
    }
}