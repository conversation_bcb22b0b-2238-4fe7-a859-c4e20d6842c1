package com.bukuwarung.widget

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.widget.Toolbar
import com.bukuwarung.R
import com.bukuwarung.databinding.WidgetToolbarBinding

class ToolbarView : Toolbar {

    var title: String
        set(value) {
            if (value.isNotBlank()) {
                binding.txtToolbarTitle.run {
                    visibility = View.VISIBLE
                    text = value
                }
            } else binding.txtToolbarTitle.visibility = View.GONE
        }
        get() = binding.txtToolbarTitle.text.toString()

    private val binding: WidgetToolbarBinding by lazy {
        WidgetToolbarBinding.inflate(LayoutInflater.from(context), this, true)
    }

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(attrs)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(attrs)
    }

    init {
        binding.root
    }

    fun backButtonListener(eventListener: () -> Unit = {}) {
        binding.backBtn.setOnClickListener {
            eventListener.invoke()
        }
    }
    fun setBackButtonResource(drawableRes: Int) {
        binding.backBtn.setImageResource(drawableRes)
    }

    fun setBackButtonVisibility(visibility: Int) {
        binding.backBtn.visibility = visibility
    }

    private fun init(attrs: AttributeSet? = null) {
        lateinit var typedArray: TypedArray
        try {
            attrs?.let {
                typedArray = context.obtainStyledAttributes(
                    it,
                    R.styleable.ToolbarView, 0, 0
                )
                val value = typedArray.getString(R.styleable.ToolbarView_android_text) ?: ""
                val backIcon = typedArray.getResourceId(
                    R.styleable.ToolbarView_back_icon,
                    R.mipmap.back_white
                )
                binding.backBtn.setImageResource(backIcon)

                val titleVisibility =
                    if (value.isNotBlank()) View.VISIBLE
                    else View.GONE

                binding.txtToolbarTitle.run {
                    text = value
                    visibility = titleVisibility
                }
            }
        } finally {
            typedArray.recycle()
        }
    }
}
