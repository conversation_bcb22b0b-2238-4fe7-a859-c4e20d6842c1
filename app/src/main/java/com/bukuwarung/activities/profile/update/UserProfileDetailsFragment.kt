package com.bukuwarung.activities.profile.update

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import androidx.lifecycle.Observer
import androidx.lifecycle.observe
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.profile.*
import com.bukuwarung.activities.profile.businessprofile.BusinessProfileWebviewActivity.Companion.createIntent
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ADDITIONAL_SECTION
import com.bukuwarung.constants.AnalyticsConst.BASIC_SECTION
import com.bukuwarung.constants.AnalyticsConst.OPERATIONAL_SECTION
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.data.restclient.CompletionRequest
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.FragmentUserProfileDetailsBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.banklist.BankAccountListActivity
import com.bukuwarung.payments.banklist.BankAccountListViewModel
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject


class UserProfileDetailsFragment : BaseFragment(), Navigator {


    private var listBankAccounts: List<BankAccount>? = null

    lateinit var userProfileOptionsAdapter: UserProfileOptionsAdapter

    @Inject
    lateinit var viewModel: ProfileTabViewModel

    @Inject
    lateinit var bankAccountListViewModel: BankAccountListViewModel
    private var _binding: FragmentUserProfileDetailsBinding? = null
    private val binding get() = _binding!!
    private var userProfileTemp: UserProfileEntity? = null
    private var userProfileFromDb: UserProfileEntity? = null
    private var basicSectionStatus: String? = null
    private var operationalSectionStatus: String? = null
    private var additionalSectionStatus: String? = null
    private var bookEntity: BookEntity? = null


    private val bookId by lazy { viewModel.getCurrentBusiness()?.bookId }
    private val customerId by lazy { null }
    private val selectedAccountId by lazy { null }

    private val delay : Long = 1000

    @Inject
    lateinit var neuro: Neuro

    companion object {
        private const val USER_ENTITY = "user_entity"

        fun instance(userProfileEntity: UserProfileEntity?): UserProfileDetailsFragment {
            val fragment = UserProfileDetailsFragment()
            val bundle = Bundle()
            bundle.putParcelable(USER_ENTITY, userProfileEntity)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View? {
        _binding = FragmentUserProfileDetailsBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun onResume() {
        initBankAccountViewModel()
        super.onResume()
    }

    override fun setupView(view: View) {

        viewModel.onEventReceived(ProfileTabViewModel.Event.GetUserProfileOptions)

        userProfileTemp = arguments?.getParcelable(USER_ENTITY)


        binding.backBtn.setOnClickListener {
            activity?.onBackPressed()
        }

        viewModel.getUserProfile(User.getUserId()).observe(this, Observer {
            if (it != null) {
                userProfileFromDb = it
                if(it.userName.isNullOrEmpty()){
                    val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                    binding.tvOwnerName.setText(if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(R.string.default_owner_name))
                }else{
                    binding.tvOwnerName.setText(it.userName)
                }

                binding.tvPhoneNumber.setText(it.userPhone)

                if (it.userProfileImageUrl.isNotNullOrEmpty())
                    setProfilePic(it.userProfileImageUrl)

                if (it.userEmail.isNotNullOrEmpty()) {
                    binding.emailLayout.visibility = View.VISIBLE
                    binding.tvEmail.text = it.userEmail
                }else{
                    binding.emailLayout.visibility = View.GONE
                }

                if (it.dateOfBirth.isNotNullOrEmpty()) {
                    binding.dobLayout.visibility = View.VISIBLE
                    val formatter = SimpleDateFormat("yyyy-MM-dd")
                    var date = formatter.parse(it.dateOfBirth)

                    val formatter_new = SimpleDateFormat("dd MMM yyyy")

                    binding.tvDob.text = formatter_new.format(date)
                }
                else{
                    binding.dobLayout.visibility = View.GONE
                }

            } else {
                binding.tvOwnerName.setText(userProfileTemp?.userName)
                binding.tvPhoneNumber.setText(userProfileTemp?.userPhone)

                if(userProfileTemp?.userProfileImageUrl.isNotNullOrEmpty())
                    setProfilePic(userProfileTemp?.userProfileImageUrl)
            }
        })
        if (bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(context)?.getBusinessByIdSync(bookId)
        }
        basicSectionStatus = Utility.sectionCompletionStatus(bookEntity, Utility.profileBasicFields)
        operationalSectionStatus = Utility.sectionCompletionStatus(bookEntity, Utility.profileInfoFields)
        additionalSectionStatus = Utility.sectionCompletionStatus(bookEntity, Utility.profileAdditionalInfoFields)
    }

    private fun initBankAccountViewModel() {
        bankAccountListViewModel.init(
            PaymentConst.TYPE_PAYMENT_IN,
            AnalyticsConst.LAINNYA_DIRECT_EXISTING,
            customerId,
            bookId,
            selectedAccountId
        )
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is ProfileTabViewModel.State.GoToRegisterBankAccount -> goToRegisterBankAccount()

                is ProfileTabViewModel.State.SetProfilePins -> {
                    showProfilePins(it.profilePins)
                }

                is ProfileTabViewModel.State.Error -> handleError(it.errorMessage,it.status)

                is ProfileTabViewModel.State.RefreshState -> {
                    userProfileOptionsAdapter.notifyDataSetChanged()
                }


                else -> {}
            }
        }

        bankAccountListViewModel.mediatorLiveData.observe(this) {
            listBankAccounts = it
        }
    }

    private fun handleError(message: String, status: Int) {
        binding.progressbar.hideView()
        if (status == 0 || status == -1) {
            //Show network error
            showErrorState(true)
        } else {
            //show server error
            showErrorState(false)
        }
    }

    private fun showErrorState(networkError : Boolean) {
        binding.errorTitle.visibility = View.VISIBLE
        binding.errorMessage.visibility = View.VISIBLE
        binding.btnRetry.visibility = View.VISIBLE
        binding.progressbar.visibility = View.GONE
        binding.errorLayout.visibility = View.VISIBLE
        binding.brickAccountSuccessLayout.visibility = View.GONE

        if(networkError){
            binding.errorImage.setImageResource(R.drawable.ic_no_internet_large)
            binding.errorTitle.setText(getString(R.string.user_profile_brick_network_error_title))
            binding.errorMessage.setText(getString(R.string.user_profile_brick_network_error_message))
        }
        else{
            binding.errorImage.setImageResource(R.drawable.ic_server_busy)
            binding.errorTitle.setText(getString(R.string.user_profile_brick_server_error_title))
            binding.errorMessage.setText(getString(R.string.user_profile_brick_server_error_message))
        }
    }

    private fun showProfilePins(pins: List<ProfilePins>) {

        userProfileOptionsAdapter = UserProfileOptionsAdapter(pins) {
            resolveDestination(pins, it)
        }
        binding.rvProfilePin.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = userProfileOptionsAdapter
        }
    }

    private fun showBrickDisconnectedState() {
        with(binding) {
            progressbar.hideView()
            brickAccountSuccessLayout.hideView()
            errorLayout.showView()
            errorTitle.invisibleView()
            errorMessage.invisibleView()
            btnRetry.hideView()
            brickNotConnectedMessage.showView()
        }
    }

    private fun showProgressState() {
        binding.progressbar.visibility = View.VISIBLE
        binding.errorLayout.visibility = View.GONE
        binding.brickAccountSuccessLayout.invisibleView()
    }


    private fun resolveDestination(pins: List<ProfilePins>, position: Int) {
        val deeplink = pins[position].deeplink

        var redirectionUrl = pins[position].deeplink_url
        if (pins[position].destination.equals("Ubah Informasi Usaha")) {
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.USER_PROFILE)
            prop.put(BASIC_SECTION,basicSectionStatus)
            prop.put(OPERATIONAL_SECTION,operationalSectionStatus)
            prop.put(ADDITIONAL_SECTION, additionalSectionStatus)
            AppAnalytics.trackEvent("edit_business_profile_click", prop)
        }

        redirect(
            redirection = redirectionUrl,
            onSuccess = {},
            onFailure = {
                when (deeplink) {
                    1 -> {
                        val profileCompletionStatus = userProfileFromDb?.userName.isNotNullOrEmpty() && userProfileFromDb?.userEmail.isNotNullOrEmpty() && userProfileFromDb?.dateOfBirth.isNotNullOrEmpty()


                        val request = CompletionRequest(AppConst.USER_PROFILE_COMPLETION)
                        if (profileCompletionStatus) {
                            viewModel.onEventReceived(ProfileTabViewModel.Event.ProfileCompletionEvent(request))
                        }

                        activity?.supportFragmentManager?.beginTransaction()?.add(R.id.main_container,
                            EditUserProfileFragment.instance(userProfileTemp))?.addToBackStack(EditUserProfileFragment.TAG)?.commit()
                    }

                    2 -> {
                        registerBankAccountClicked()
                    }

                    3 -> {
                        val prop = AppAnalytics.PropBuilder()
                            .put("kyc_status", if(FeaturePrefManager.getInstance().hasCompletedKYC()){"complete"} else {"incomplete"})
                        AppAnalytics.trackEvent("profile_detail_click_kyc",prop)
                        deeplink?.let {
                            startActivity(
                                WebviewActivity.createIntent(
                                    requireActivity(), redirectionUrl,
                                    pins[position].name
                                )
                            )
                        }

                    }

                    4 -> {
                        openBusinessProfileWebView()
                    }


                    else -> {}
                }
            },
        )
    }

    private fun redirect(
        redirection: String,
        onSuccess: () -> Unit,
        onFailure: () -> Unit,
    ) {
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = onSuccess,
            onFailure = {
                redirectWithLegacyLink(redirection, onSuccess, onFailure)
            },
        )
    }

    private fun redirectWithLegacyLink(
        redirection: String,
        onSuccess: () -> Unit,
        onFailure: () -> Unit,
    ) {
        var url = redirection

        if (url.isNotNullOrEmpty() && url.startsWith("redirectTo:")) {
            url = url.replace("redirectTo:", "")
            val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$url&launch=2"
            val sourceLink = SourceLink(context = requireContext(), link)

            neuro.route(
                sourceLink,
                navigator = this,
                onSuccess = onSuccess,
                onFailure = {},
            )
            return
        }

        onFailure()
    }

    private fun openBusinessProfileWebView() {
        if(RemoteConfigUtils.getBusinessProfileVariant() == RemoteConfigConst.BUSINESS_PROFILE_VARIANT_NEW_WEB) {
            val webViewIntent = createIntent(context)
            requireContext().startActivity(webViewIntent)
        }else {
            try{
                val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                val completionPercentage = Utility.calculateCompletionPercentage(bookEntity)


                val request: CompletionRequest = CompletionRequest(AppConst.BUSINESS_PROFILE_COMPLETION)
                if (completionPercentage >= 100) {
                    viewModel.onEventReceived(ProfileTabViewModel.Event.ProfileCompletionEvent(request))
                }
            }catch (e:Exception){
                FirebaseCrashlytics.getInstance().recordException(e)
            }

            val webViewIntent = NgBusinessProfileActivity.createIntent(
                context,
                User.getBusinessId()
            )
            requireContext().startActivity(webViewIntent)
        }
    }

    private fun setProfilePic(imageUri: String?) {
        Glide.with(this).load(imageUri)
            .apply(RequestOptions().circleCrop())
            .placeholder(R.drawable.ic_icon_shop)
            .error(R.drawable.ic_icon_shop)
            .into(binding.profilePic)
    }

    private fun registerBankAccountClicked() {
        viewModel.onEventReceived(ProfileTabViewModel.Event.OnRegisterBankAccount)
    }

    private fun goToRegisterBankAccount() {

        if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_ADD_BANK_ACCOUNT)) {
            PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.LAINNYA)
            return
        }

        if (listBankAccounts?.isEmpty() == true || listBankAccounts == null) {
            val i = activity?.let {
                AddBankAccountActivity.createIntent(
                    it,
                    PaymentConst.TYPE_PAYMENT_IN.toString(),
                    bookId,
                    AnalyticsConst.LAINNYA_DIRECT_FIRST,
                    hasBankAccount = "false"
                )
            }
            startActivity(i)
        } else {
            AppConfigManager.getInstance().setBankLayoutBaru()

            val prop = AppAnalytics.PropBuilder()
                .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.USER_PROFILE_MENU)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_OPEN_USER_BANK_LIST,prop)

            val i = activity?.let {
                BankAccountListActivity.createIntent(
                    it,
                    PaymentConst.TYPE_PAYMENT_IN.toString(),
                    bookId,
                    AnalyticsConst.LAINNYA_DIRECT_EXISTING,
                    isSelfOnly = "true"
                )
            }
            startActivity(i)
        }
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}