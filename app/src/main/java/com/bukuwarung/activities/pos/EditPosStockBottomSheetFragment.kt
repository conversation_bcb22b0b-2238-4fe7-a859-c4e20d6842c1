package com.bukuwarung.activities.pos

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import com.bukuwarung.R
import com.bukuwarung.activities.pos.viewmodel.PosClickEvent
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.BottomSheetDialogPosEditStockBinding
import com.bukuwarung.keyboard.CustomKeyboardView
import com.bukuwarung.utils.*
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import dagger.android.support.AndroidSupportInjection

import javax.inject.Inject

class EditPosStockBottomSheetFragment(
    val posClickEvent: PosClickEvent?,
    val isCartMode: Boolean = false,
    private val onConfirm: (PosClickEvent?) -> Unit
) : BottomSheetDialogFragment() {

    @Inject
    lateinit var viewModel: PosViewModel
    private var isFav = false
    var productId: String = ""
    var productQty: Double = 0.0
    var oldQty: Double = 0.0
    var count: Double = 0.0
    var productName: String = ""
    var productPrice: Double = 0.0
    var trackInventoryStatus = AppConst.INVENTORY_TRACKING_ENABLED

    private lateinit var binding: BottomSheetDialogPosEditStockBinding

    private lateinit var keyboard: CustomKeyboardView

    /**
     * Remote config
     */
    private var isStockToggleEnabled = RemoteConfigUtils.isStockToggleEnabled()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        posClickEvent?.let {
            val productEntity = it.product.product
            productId = productEntity.productId
            isFav = productEntity.favourite
            productQty = it.product.product.stock
            oldQty = it.product.count
            productPrice = productEntity.unitPrice
            productName = productEntity.name
            count = it.product.count
            trackInventoryStatus = productEntity.trackInventory
        }
        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialogThemeRound)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        AndroidSupportInjection.inject(this)

        binding = BottomSheetDialogPosEditStockBinding.inflate(inflater, container, false)
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.ivClose.setOnClickListener {
            InputUtils.hideKeyboardFrom(context, binding.numberStepperLayout.etNumber)
            dialog?.dismiss()
        }

        if (isStockToggleEnabled && trackInventoryStatus == AppConst.INVENTORY_TRACKING_DISABLED) {
            binding.tvStockValue.visibility = View.GONE
            binding.tvStock.visibility = View.GONE
        }

//        initKeyboard()

        binding.numberStepperLayout.btnIncrease.setOnClickListener {
            try {
                InputUtils.hideKeyboardFrom(context, binding.numberStepperLayout.etNumber)
                binding.numberStepperLayout.etNumber.clearFocus()
                var olderValue: Double =
                    if (binding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                        0.0
                    } else {
                        Utility.extractAmountFromText(binding.numberStepperLayout.etNumber.text.toString())
                    }

                olderValue++
                binding.numberStepperLayout.etNumber.setText(Utility.getRoundedOffPrice(olderValue))
                productQty = olderValue
                posClickEvent?.newCount = olderValue
            } catch (e: Exception) {
                Log.v("exception", e.toString())
            }
        }

//        binding.numberStepperLayout.etNumber.setOnFocusChangeListener { view, b ->
//            if (b) {
//                changeKeyboardFocus()
//            } else {
//                hideKeyBoard()
//            }
//        }

        binding.numberStepperLayout.btnDecrease.setOnClickListener {
            try {
                InputUtils.hideKeyboardFrom(context, binding.numberStepperLayout.etNumber)
                binding.numberStepperLayout.etNumber.clearFocus()
                var olderValue: Double =
                    if (binding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                        0.0
                    } else {
                        Utility.extractAmountFromText(binding.numberStepperLayout.etNumber.text.toString())
                    }

                if (olderValue > 0) {
                    olderValue--
                    binding.numberStepperLayout.etNumber.setText(
                        Utility.getRoundedOffPrice(
                            olderValue
                        )
                    )
                    productQty = olderValue
                    posClickEvent?.newCount = olderValue
                }
            } catch (e: Exception) {
                Log.v("exception", e.toString())
            }
        }

        binding.labelFavourite.setOnClickListener {
            trackFavoriteButtonClick()

            if (!isFav) {
                Toast.makeText(
                    requireContext(),
                    getString(R.string.added_to_favorite_message),
                    Toast.LENGTH_SHORT
                ).apply {
                    setGravity(Gravity.TOP,0 ,0)
                }.show()
            }

            val bg = if (isFav) R.drawable.button_round_cornerd_edit_stroke_pop_up else R.drawable.bg_yellow_stroke_f7b500
            binding.labelFavourite.background = requireContext().getDrawableCompat(bg)

            val icon = if (isFav) R.drawable.ic_fav_grey else R.drawable.ic_yellow_fav
            binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(icon, 0, 0, 0)
            isFav = !isFav
        }
        binding.btnConfirm.setOnClickListener {
            val oldCount = productQty
            val newCount: Double =
                if (binding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                    0.0
                } else {
                    Utility.extractAmountFromText(binding.numberStepperLayout.etNumber.text.toString())
                }
            productQty = newCount
            posClickEvent?.newCount = newCount
            posClickEvent?.isFavourite = isFav

            val updateType = if (newCount == oldQty) {
                AnalyticsConst.NO_CHANGE
            } else {
                AnalyticsConst.EDIT_QUANTITY
            }

            trackProductDetailsUpdatedEvent(updateType, newCount)

            onConfirm.invoke(posClickEvent)
            dismiss()
        }

        binding.tvEmptyCart.setOnClickListener {
            posClickEvent?.newCount = 0.0
            trackProductDetailsUpdatedEvent(AnalyticsConst.DELETE_PRODUCT, 0.0)
            onConfirm.invoke(posClickEvent)
            dismiss()
        }
        if (isFav) {
            binding.labelFavourite.background = binding.labelFavourite.context?.let {
                ContextCompat.getDrawable(
                    it, R.drawable
                        .bg_yellow_stroke_f7b500
                )
            }
            binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.ic_yellow_fav,
                0,
                0,
                0
            )
        } else {
            binding.labelFavourite.background = binding.labelFavourite.context?.let {
                ContextCompat.getDrawable(
                    it, R.drawable
                        .button_round_cornerd_edit_stroke_pop_up
                )
            }
            binding.labelFavourite.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.ic_fav_grey,
                0,
                0,
                0
            )
        }
        binding.numberStepperLayout.etNumber.setText(Utility.getRoundedOffPrice(count))
        binding.productName.text = productName.capitalize()
        val measurementUnit = posClickEvent?.product?.product?.measurementName ?: ""
        binding.tvStockValue.text = if (isCartMode) "%s %s".format(
            Utility.getRoundedOffPrice(productQty),
            measurementUnit
        ) else Utility.getRoundedOffPrice(productQty)
        binding.tvProductPriceValue.text = Utility.formatAmount(productPrice)

        return binding.root
    }

    private fun trackFavoriteButtonClick() {
        trackEvent(AnalyticsConst.MARK_FAVOURITE_PRODUCT){
            addEntryPointProperty(AnalyticsConst.CASHIER_MODE)
            val action = if (isFav) AnalyticsConst.REMOVE else AnalyticsConst.MARK
            addProperty(AnalyticsConst.ACTION to action)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            Utilities.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
        }
    }

    private fun trackProductDetailsUpdatedEvent(updateType: String, newQty: Double) {
        val entryPoint = if (isCartMode) {
            AnalyticsConst.CART
        } else {
            AnalyticsConst.STOREFRONT
        }
        val qtyType = Utility.getQuantityTypeFromTotalStock(newQty)

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.UPDATE_TYPE, updateType)
        propBuilder.put(AnalyticsConst.OLD_QUANTITY, oldQty)
        propBuilder.put(AnalyticsConst.NEW_QUANTITY, newQty)
        propBuilder.put(AnalyticsConst.PRODUCT_NAME, productName)
        propBuilder.put(AnalyticsConst.QUANTITY_TYPE, qtyType)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, entryPoint)
        AppAnalytics.trackEvent(AnalyticsConst.POS_UPDATE_PRODUCT_DETAILS_SAVED, propBuilder)
    }

    fun hideKeyBoard() {
        keyboard?.apply {
            if (this.isAttachedToWindow) {
                visibility = View.GONE
                submit()
            }
        }
    }

    private fun changeKeyboardFocus() {
        binding.numberStepperLayout.etNumber.clearFocus()
        keyboard.apply {
            setCurrency(binding.tvCurrency)
            binding.tvExpr.text = ""
            setResultLayout(binding.llResult)
            keyboard.setResultTv(binding.numberStepperLayout.etNumber)
            val moveup = AnimationUtils.loadAnimation(context, R.anim.move_up)
            if (context.isAnimEnabled()) startAnimation(moveup)
            visibility = View.VISIBLE
        }


    }

    private fun initKeyboard() {
        keyboard = binding.keyboardView
        keyboard?.apply {
            cursor = binding.cursor
            setCurrency(binding.tvCurrency)
            setExprTv(binding.tvExpr)
            setResultLayout(binding.llResult)
            binding.llResult.hideView()
        }
    }

}