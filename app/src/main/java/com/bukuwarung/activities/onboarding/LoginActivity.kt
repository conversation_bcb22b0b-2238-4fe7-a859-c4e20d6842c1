package com.bukuwarung.activities.onboarding

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.text.Html
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityLoginBinding
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.enums.OtpRequestStatus
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.tracking.TraceConstants
import com.bukuwarung.utils.*
import com.google.firebase.perf.metrics.AddTrace
import javax.inject.Inject

class LoginActivity : BaseActivity() {
    private lateinit var binding: ActivityLoginBinding
    private val shouldShowDefaultOtpLayout = RemoteConfigUtils.shouldShowDefaultOtpScreen()
    companion object {
        private const val SKIP_TO_LOGIN = "skipToLogin"
        private const val SKIP_TO_LOGIN_DEEPLINK = "skipToLoginDeeplink"
        private const val LOGIN_PARAM_REFERRAL_CODE = "referralcode"
        private const val LOGIN_PARAM_DESTINATION = "destination"
        private var isValueSent = false

        fun getIntent(origin: Activity?, referralCode: String?, destination: String?): Intent? {
            val intent = Intent(origin, LoginActivity::class.java)
            intent.putExtra(LOGIN_PARAM_REFERRAL_CODE, referralCode)
            intent.putExtra(LOGIN_PARAM_DESTINATION, destination)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.putExtra(SKIP_TO_LOGIN_DEEPLINK, true)
            return intent
        }

        fun createIntent(origin: Activity, referralCode: String? = null, skipToLogin: Boolean = false): Intent {
            val i = Intent(origin, LoginActivity::class.java)
            i.putExtra(LOGIN_PARAM_REFERRAL_CODE, referralCode)
            i.putExtra(SKIP_TO_LOGIN, skipToLogin)
            return i
        }
    }

    private var autoDetectOTP: AutoDetectOTP? = null

    @Inject
    lateinit var viewModel: LoginViewModel
    override fun setViewBinding() {
        binding = ActivityLoginBinding.inflate(layoutInflater)
    }

    @AddTrace(name = TraceConstants.setUpViewOldLogin, enabled = true)
    override fun setupView() {

        viewModel.onEventReceived(LoginViewModel.Event.OnCreateView)
        setContentView(binding.root)

        try {
            viewModel.onEventReceived(LoginViewModel.Event.OnLoadAppConfigRequest)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        val customWelcomeText = AppConfigManager.getInstance().welcomeText
        if (!customWelcomeText.isNullOrBlank()) {
            binding.welcomeText.text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N)
                Html.fromHtml(customWelcomeText, Html.FROM_HTML_MODE_COMPACT)
            else Html.fromHtml(customWelcomeText)
        }
        binding.phoneET.afterTextChanged {
            val enable = it.isNotBlank() && it.length > 5
            binding.btnWa.isEnabled = enable
            binding.btnSms.isEnabled = enable
            binding.btnNext.isEnabled = enable
        }
        try {
            autoDetectOTP = AutoDetectOTP(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        binding.countryPicker.setCountryForPhoneCode(62)
        binding.countryPicker.setOnCountryChangeListener {
            viewModel.onEventReceived(LoginViewModel.Event.OnCountryPicked(binding.countryPicker.selectedCountryCodeWithPlus))
        }
        binding.loadingPanel?.visibility = View.GONE
        binding.inputLayout?.visibility = View.VISIBLE

        try {
            if (AppConfigManager.getInstance().enableGuestFeature == 0) {
                binding.skipLogin.visibility = View.GONE
            }else{
                binding.skipLogin.visibility = View.VISIBLE
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        binding.btnSms?.setOnClickListener {
            checkBeforeRequestOtp(NotificationChannel.SMS.value)
        }
        binding.btnWa?.setOnClickListener {
            checkBeforeRequestOtp(NotificationChannel.WA.value)
        }

        binding.skipLogin?.setOnClickListener {
            if (AppConfigManager.getInstance().enableGuestFeature == 0) {
                NotificationUtils.alertToast(getString(R.string.guest_user_disabled_warning))
                return@setOnClickListener
            }
            binding.loadingPanel.visibility = View.VISIBLE
            binding.inputLayout.visibility = View.GONE
            binding.verifyTxt.text = getString(R.string.preparing)
            viewModel.onEventReceived(LoginViewModel.Event.OnRequestGuestSession(binding.countryPicker.selectedCountryCodeWithPlus))
        }

        binding.btnWa.visibility = shouldShowDefaultOtpLayout.asVisibility()
        binding.btnSms.visibility = shouldShowDefaultOtpLayout.asVisibility()
        binding.txtSendCode.visibility = shouldShowDefaultOtpLayout.asVisibility()
        binding.btnNext.visibility = (!shouldShowDefaultOtpLayout).asVisibility()

        val otpChannel = RemoteConfigUtils.getOtpChannel()
        binding.btnNext.setOnClickListener {
            checkBeforeRequestOtp(otpChannel)
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is LoginViewModel.State.Error -> handleError(it.message, it.phone, it.method, it.status)
                is LoginViewModel.State.GoToVerifyOtp -> goToVerifyOtp(it.phone, it.countryCode, it.method, it.channel)
                is LoginViewModel.State.GoToVerifyPassword -> goToVerifyPassword(it.phone, it.countryCode, it.method, it.channel, it.showCaptcha)
                is LoginViewModel.State.SkipUserLogin -> skipUserLogin(it.userId, it.propBuilder)
                is LoginViewModel.State.SetSkipLoginButton -> skipLoginButtonVisibility(it.skipButton)
                else -> {}
            }
        }
    }

    private fun skipLoginButtonVisibility(visibility: Boolean) {
        if (visibility) {
            binding.skipLogin.visibility = View.VISIBLE
        } else {
            binding.skipLogin.visibility = View.GONE
        }
    }

    private fun handleError(message: String, phone: String, method: String, status:OtpRequestStatus) {
        val propBuilder = PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status.name)
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.ERROR_MESSAGE, message)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)

        binding.warningText.text = message
        binding.warningText.visibility = View.VISIBLE
        binding.loadingPanel.visibility = View.GONE
        binding.inputLayout.visibility = View.VISIBLE
    }

    private fun checkBeforeRequestOtp(method: String) {
        val phone = Utility.cleanPhonenumber(binding.phoneET?.text.toString())
        try {
            if (!Utility.hasInternet()) {
                binding.warningText.text = getString(R.string.no_internet_error)
                binding.warningText.visibility = View.VISIBLE
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                propBuilder.put(AnalyticsConst.DETAIL, AnalyticsConst.DETAIL_NO_NETWORK)
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
                propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
                return
            }
            if (Utility.loginExceeded()) {
                binding.warningText.text = getString(R.string.login_exceeded_error)
                binding.warningText.visibility = View.VISIBLE
                return
            }
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_START)
            propBuilder.put(AnalyticsConst.DETAIL, phone)
            propBuilder.put(AnalyticsConst.OTP_METHOD, method)
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
            propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        } catch (e: Exception) {
            binding.warningText.text = getString(R.string.general_device_error)
            binding.warningText.visibility = View.VISIBLE
        }
        binding.warningText.visibility = View.GONE
        binding.warningText.text = null
        binding.verifyTxt.text = getString(R.string.verifying_number)
        binding.loadingPanel.visibility = View.VISIBLE
        binding.inputLayout.visibility = View.GONE

        if (RemoteConfigUtils.getAuthVariant() == 0) {
            viewModel.onEventReceived(LoginViewModel.Event.OnRequestOTP(phone, method))
        } else {
            viewModel.onEventReceived(LoginViewModel.Event.OnCheckPasswordExists(phone, method))
        }
    }

    private fun goToVerifyOtp(phone: String, countryCode: String, method: String, channel: List<String>?) {
        val propBuilder = PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_SENT_OTP)
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        val loginMethod = SessionManager.getInstance().loginMethod
        val intent = VerifyOtpActivity.createIntent(this, phone, "-", countryCode, !(channel?.contains(loginMethod) ?: false))
        startActivity(intent)
        // if finished, then trycount always reset after going to verify otp activity. expected?
        finish()
    }

    private fun goToVerifyPassword(phone: String, countryCode: String, method: String, channel: List<String>?, showCaptcha: Boolean) {
        val loginMethod = SessionManager.getInstance().loginMethod
        val intent = com.bukuwarung.feature.login.password.screen.PasswordActivity.createIntent(
            this,
            phone,
            "-",
            countryCode,
            !(channel?.contains(loginMethod) ?: false),
            "",
            showCaptcha,
        )
        startActivity(intent)
        // if finished, then trycount always reset after going to verify otp activity. expected?
        finish()
    }

    private fun skipUserLogin(userId: String, propBuilder: PropBuilder) {
        AppAnalytics.trackEvent(AnalyticsConst.SKIP_LOGIN, propBuilder)
        MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
        // if finished, then trycount always reset after going to verify otp activity. expected?
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == AutoDetectOTP.RC_HINT) {
            if (resultCode == RESULT_OK) {
                fillPhoneNoCountryCode(autoDetectOTP?.getPhoneNo(data))
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        isValueSent = false
    }

    override fun onResume() {
        super.onResume()
        val intent = intent
        try {
            if (!isValueSent  && intent.action === Intent.ACTION_VIEW && intent.type.equals(getString(R.string.from_splash_login), ignoreCase = true)) {
                isValueSent = true
            }
        } catch (e: java.lang.Exception) {
        }
    }

    private fun fillPhoneNoCountryCode(str: String?) {
        try {
            if (str.isNullOrBlank()) return
            var purifyMobileNumber = Utility.cleanPhonenumber(str)
            purifyMobileNumber = InputUtils.removedCountryCodeFromMobileNo(purifyMobileNumber)
            val findCountryCodeFromMobileNo = InputUtils.findCountryCodeFromMobileNo(str)
            val prop = PropBuilder()
            prop.put(AnalyticsConst.COUNTRY_CD, findCountryCodeFromMobileNo)
            prop.put(AnalyticsConst.NUMBER, purifyMobileNumber)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_PHONE_NUMBER_AUTO, prop)
            if (!findCountryCodeFromMobileNo.isNullOrBlank()) {
                binding.countryPicker.setCountryForPhoneCode(findCountryCodeFromMobileNo.substring(1).toInt())
            }
            if (!purifyMobileNumber.isNullOrBlank()) {
                binding.phoneET.setText(purifyMobileNumber)
                binding.phoneET.setSelection(purifyMobileNumber.length)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PHONE_SELECTED_FROM_HINT)
            }

        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            e.recordException()
        }
    }
}
