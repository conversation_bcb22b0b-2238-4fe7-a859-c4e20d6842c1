package com.bukuwarung.activities.onboarding

import android.app.Activity
import android.content.ComponentName
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.ScrollView
import androidx.core.view.isVisible
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.maintainance.MaintenanceActivity
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.LINK_NAME
import com.bukuwarung.constants.AnalyticsConst.PRIVACY_POLICY_ID
import com.bukuwarung.constants.AnalyticsConst.TERMS_AND_CONDITIONS_ID
import com.bukuwarung.constants.AnalyticsConst.TIME_TNC_AGREE
import com.bukuwarung.constants.AnalyticsConst.TIME_TNC_CHECKED
import com.bukuwarung.constants.AnalyticsConst.TNC_CHECKBOX
import com.bukuwarung.databinding.ActivityLoginNewBinding
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.enums.OtpRequestStatus
import com.bukuwarung.feature.login.password.screen.PasswordActivity
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.AppMaintenanceData
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.tracking.TraceConstants
import com.bukuwarung.tutor.prefs.PreferencesManager
import com.bukuwarung.ui.BukuAgenDialog
import com.bukuwarung.utils.*
import com.bukuwarung.utils.extensions.loadCircleImage
import com.google.firebase.perf.metrics.AddTrace
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.*
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEventListener
import java.util.Calendar
import javax.inject.Inject

class NewLoginActivity : BaseActivity() {
    private var variant: Int = 1
    private lateinit var binding: ActivityLoginNewBinding
    private val shouldShowDefaultOtpLayout = RemoteConfigUtils.shouldShowDefaultOtpScreen()
    private var loadingDialog : GeneralLoadingDialog? = null
    private var isTncAccepted = false
    private var isPrivacyPolicy = false

    lateinit var span1: ClickableSpan
    lateinit var span2: ClickableSpan
    lateinit var span3: ClickableSpan

    companion object {
        private const val SKIP_TO_LOGIN = "skipToLogin"
        private const val SKIP_TO_LOGIN_DEEPLINK = "skipToLoginDeeplink"
        private const val LOGIN_PARAM_REFERRAL_CODE = "referralcode"
        private const val LOGIN_PARAM_DESTINATION = "destination"
        private const val TNC_LINK = "tnc_link"
        private const val PRIVACY_LINK = "privacy_link"
        private var isValueSent = false

        fun getIntent(origin: Activity?, referralCode: String?, destination: String?): Intent? {
            val intent = Intent(origin, NewLoginActivity::class.java)
            intent.putExtra(LOGIN_PARAM_REFERRAL_CODE, referralCode)
            intent.putExtra(LOGIN_PARAM_DESTINATION, destination)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.putExtra(SKIP_TO_LOGIN_DEEPLINK, true)
            return intent
        }

        fun createIntent(origin: Activity, referralCode: String? = null, skipToLogin: Boolean = false): Intent {
            val i = Intent(origin, NewLoginActivity::class.java)
            i.putExtra(LOGIN_PARAM_REFERRAL_CODE, referralCode)
            i.putExtra(SKIP_TO_LOGIN, skipToLogin)
            return i
        }
    }

    private var autoDetectOTP: AutoDetectOTP? = null

    @Inject
    lateinit var viewModel: LoginViewModel
    override fun setViewBinding() {
        binding = ActivityLoginNewBinding.inflate(layoutInflater)

    }

    @AddTrace(name = TraceConstants.setUpNewLogin, enabled = true)
    override fun setupView() {

        val showSessionEndedDialog = intent.getBundleExtra("DATA")?.getBoolean("show_session_ended_dialog").isTrue
        if (showSessionEndedDialog) showSessionEndedDialog()

        span1 = object : ClickableSpan() {
            override fun onClick(textView: View) {
                LoginTncDialog(RemoteConfigUtils.getTnCUrl(), this@NewLoginActivity, callback = {
                    isTncAccepted = true
                    binding.tncTv.isChecked = isPrivacyPolicy
                }).show()
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
            Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            if (!PermissonUtil.hasPhoneStatePermission()) {
                PermissonUtil.requestPhoneStatePermission(this@NewLoginActivity)
            }
        }

        span2 = object : ClickableSpan() {
            override fun onClick(textView: View) {
                LoginTncDialog(
                    RemoteConfigUtils.getPrivacyPolicyUrl(),
                    this@NewLoginActivity,
                    callback = {
                        isPrivacyPolicy = true
                        binding.tncTv.isChecked = isTncAccepted
                    }).show()
            }
        }

        viewModel.onEventReceived(LoginViewModel.Event.OnCreateView)
        setContentView(binding.root)

        try {
            viewModel.onEventReceived(LoginViewModel.Event.OnLoadAppConfigRequest)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        binding.phoneET.afterTextChanged {
            binding.tncTv.isChecked = FeaturePrefManager.getInstance().getHasUserLoggedIn(it)
            binding.btnNext.isEnabled = Utilities.validatePhoneNumber(it,binding.countryPicker.selectedCountryCode) && binding.tncTv.isChecked
        }
        binding.tncTv.setOnCheckedChangeListener { compoundButton, isChecked ->
            if (isChecked) {
                binding.btnNext.isEnabled = Utilities.validatePhoneNumber(
                    binding.phoneET.text.toString(),
                    binding.countryPicker.selectedCountryCode
                )
            } else {
                binding.btnNext.isEnabled = false
            }
        }
        try {
            autoDetectOTP = AutoDetectOTP(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        binding.countryPicker.setCountryForPhoneCode(62)
        binding.countryPicker.setOnCountryChangeListener {
            Utilities.validatePhoneNumber(binding.phoneET.text.toString(), binding.countryPicker.selectedCountryCode)
            viewModel.onEventReceived(LoginViewModel.Event.OnCountryPicked(binding.countryPicker.selectedCountryCodeWithPlus))
        }
        val otpChannel = RemoteConfigUtils.getOtpChannel()
        binding.btnNext.apply {
            setSingleClickListener {
                checkBeforeRequestOtp(otpChannel)
            }
        }

        setupKeyboardListener()

        binding.userCountLayout.chipUserCount.text = RemoteConfigUtils.getUserCountForWelcomeScreen()
        binding.userCountLayout.imgUser1.loadCircleImage(R.drawable.image_profile_0)
        binding.userCountLayout.imgUser2.loadCircleImage(R.drawable.image_profile_1)
        binding.userCountLayout.imgUser3.loadCircleImage(R.drawable.image_profile_2)

        val appMaintenance = RemoteConfigUtils.getAppMaintenanceData()
        val gson = GsonBuilder().create()
        val jsonType = object : TypeToken<AppMaintenanceData?>() {}.type
        val appMaintenanceData = gson.fromJson<AppMaintenanceData>(appMaintenance, jsonType)
        if (appMaintenanceData.showAppMaintenance!!) {
            if (appMaintenanceData.unblockedNumbers != null && appMaintenanceData.unblockedNumbers.size > 0) {
                for (i in appMaintenanceData.unblockedNumbers.indices) {
                    if (User.getUserId() == appMaintenanceData.unblockedNumbers[i]) {
                        appMaintenanceData.showAppMaintenance = false
                    }
                }
            }
        }
        AppConfigManager.getInstance()
            .isAppUnderMaintenance(appMaintenanceData.showAppMaintenance!!)

        if (appMaintenanceData.showAppMaintenance != null && appMaintenanceData.showAppMaintenance!!) {
            val intent = Intent(Application.getAppContext(), MaintenanceActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            Application.getAppContext().startActivity(intent)
        }

        setupTnc()
    }

    private fun setupTnc() {

        if(RemoteConfigUtils.isTncVisible())
        {
            binding.tncTv.visibility = View.VISIBLE
            setTnCWithoutCheckBox()
        }
    }

    private fun setTnCWithoutCheckBox() {
        val ss = SpannableString(getString(R.string.terms_and_conditions_without_cb))

        ss.setSpan(span1, 35, 55, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        ss.setSpan(span2, 62, 79, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        binding.tncTv.setText(ss)
        binding.tncTv.setMovementMethod(LinkMovementMethod.getInstance())
    }

    private fun setupKeyboardListener() {
        binding.contentScroll.setOnTouchListener { _, _ -> true }
        KeyboardVisibilityEvent.setEventListener(this, object : KeyboardVisibilityEventListener {
            override fun onVisibilityChanged(isOpen: Boolean) {
                binding.contentScroll.fullScroll(ScrollView.FOCUS_DOWN)
            }
        })
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is LoginViewModel.State.Error -> handleError(it.message, it.phone, it.method, it.status,it.isUserBlocked)
                is LoginViewModel.State.GoToVerifyOtp -> goToVerifyOtp(it.phone, it.countryCode, it.method, it.channel)
                is LoginViewModel.State.GoToVerifyPassword -> goToVerifyPassword(it.phone, it.countryCode, it.method, it.channel, it.userId, it.showCaptcha)
                is LoginViewModel.State.SkipUserLogin -> skipUserLogin(it.userId, it.propBuilder)
                is LoginViewModel.State.SetSkipLoginButton -> {/*skipLoginButtonVisibility(it.skipButton)*/ }
                else -> {}
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun goToVerifyPassword(
        phone: String,
        countryCode: String,
        method: String,
        channel: List<String>?,
        userId: String,
        showCaptcha: Boolean,
    ) {
        val propBuilder = PropBuilder()
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(TNC_CHECKBOX, AnalyticsConst.CHECKED)
        propBuilder.put(TIME_TNC_CHECKED, Calendar.getInstance().time)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        propBuilder.put(AnalyticsConst.ALREADY_SET_PASSWORD, true)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)

        loadingDialog?.dismiss()
        val loginMethod = SessionManager.getInstance().loginMethod
        val intent = PasswordActivity.createIntent(this, phone, "-", countryCode, !(channel?.contains(loginMethod)
            ?: false),userId,showCaptcha)
        startActivity(intent)
        // if finished, then trycount always reset after going to verify otp activity. expected?
//        finish()
    }

    private fun handleError(message: String, phone: String, method: String, status:OtpRequestStatus,isUserBlocked : Boolean) {
        val propBuilder = PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status.name)
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.ERROR_MESSAGE, message)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)

        binding.warningText.visibility = View.VISIBLE
        loadingDialog?.dismiss()
        if(isUserBlocked){
            span3 = object : ClickableSpan() {
                override fun onClick(textView: View) {
                    openWaBotHelp()
//                    goToWhatsAppForCustomerService()
                }
            }

            val ss = SpannableString(getString(R.string.please_try_after_sometime_error))

            ss.setSpan(span3, 81, 98, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

            binding.warningText.text = ss
            binding.warningText.setMovementMethod(LinkMovementMethod.getInstance())
        }else{
            binding.warningText.text = message
        }
    }

    private fun openWaBotHelp() {
        var bundle = Bundle()
        bundle.putString(AnalyticsConst.ENTRY_POINT,AnalyticsConst.LOGIN_PAGE)
        WhatsAppUtils.openWABotWithHelpText(this, getString(R.string.wa_help_text_general), bundle)
    }

    private fun goToWhatsAppForCustomerService() {
        val sendIntent = Intent("android.intent.action.MAIN")
        sendIntent.component = ComponentName("com.whatsapp", "com.whatsapp.Conversation")
        sendIntent.action = Intent.ACTION_SEND
        sendIntent.type = "text/plain"
        sendIntent.putExtra(Intent.EXTRA_TEXT, "")
        sendIntent.putExtra("jid", AppConfigManager.getInstance().whatsappId + "@s.whatsapp.net")
        sendIntent.setPackage("com.whatsapp")
        startActivity(sendIntent)
    }

    private fun checkBeforeRequestOtp(method: String) {
        val phone = Utility.cleanPhonenumber(binding.phoneET?.text.toString())
        try {
            if (!Utility.hasInternet()) {
                binding.warningText.text = getString(R.string.no_internet_error)
                binding.warningText.visibility = View.VISIBLE
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                propBuilder.put(AnalyticsConst.DETAIL, AnalyticsConst.DETAIL_NO_NETWORK)
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
                propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
                return
            }
            if (Utility.loginExceeded()) {
                binding.warningText.text = getString(R.string.login_exceeded_error)
                binding.warningText.visibility = View.VISIBLE
                return
            }
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_START)
            propBuilder.put(AnalyticsConst.DETAIL, phone)
            propBuilder.put(AnalyticsConst.OTP_METHOD, method)
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
            propBuilder.put(TNC_CHECKBOX, "checked")
            propBuilder.put(TIME_TNC_CHECKED, Calendar.getInstance().time)
            propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        } catch (e: Exception) {
            binding.warningText.text = getString(R.string.general_device_error)
            binding.warningText.visibility = View.VISIBLE
        }
        FeaturePrefManager.getInstance().setUserHasLoggedIn(binding.phoneET?.text.toString())
        binding.warningText.visibility = View.GONE
        binding.warningText.text = null
        loadingDialog = GeneralLoadingDialog.createInstance()
        loadingDialog?.show(supportFragmentManager, GeneralLoadingDialog.TAG)

        if (RemoteConfigUtils.getAuthVariant() == 0) {
            viewModel.onEventReceived(LoginViewModel.Event.OnRequestOTP(phone, method))
        } else {
            viewModel.onEventReceived(LoginViewModel.Event.OnCheckPasswordExists(phone, method))
        }
    }

    private fun goToVerifyOtp(phone: String, countryCode: String, method: String, channel: List<String>?) {
        loadingDialog?.dismiss()
        //add tnc state to sharedPref
        if (binding.tncTv.isVisible) {
            PreferencesManager(this).setTnCAccepted(true)
        } else {
            PreferencesManager(this).setTnCAccepted(false)
        }

        val propBuilder = PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_SENT_OTP)
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        propBuilder.put(AnalyticsConst.ALREADY_SET_PASSWORD, false)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        val intent = NewVerifyOtpActivity.createIntent(this, phone, "-", countryCode)

        // Navigate to password Activity
//        val intent = PasswordActivity.createIntent(this, phone, "-", countryCode, !(channel?.contains(loginMethod)
//                ?: false))
//        startActivity(intent)

//         Navigate to create password Activity
//        val intent = CreateNewPasswordActivity.createIntent(this, phone, "-", countryCode, !(channel?.contains(loginMethod)
//                ?: false))
        startActivity(intent)
        // if finished, then trycount always reset after going to verify otp activity. expected?
        finish()
    }

    private fun skipUserLogin(userId: String, propBuilder: PropBuilder) {
        AppAnalytics.trackEvent(AnalyticsConst.SKIP_LOGIN, propBuilder)
        MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
        // if finished, then trycount always reset after going to verify otp activity. expected?
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == AutoDetectOTP.RC_HINT) {
            if (resultCode == RESULT_OK) {
                fillPhoneNoCountryCode(autoDetectOTP?.getPhoneNo(data))
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        isValueSent = false
    }

    override fun onResume() {
        super.onResume()
        val intent = intent
        try {
            if (!isValueSent  && intent.action === Intent.ACTION_VIEW && intent.type.equals(getString(R.string.from_splash_login), ignoreCase = true)) {
                isValueSent = true
            }
        } catch (e: java.lang.Exception) {
        }
    }

    @AddTrace(name = TraceConstants.fillPhoneNoCountryCode, enabled = true)
    private fun fillPhoneNoCountryCode(str: String?) {
        try {
            if (str.isNullOrBlank()) return
            var purifyMobileNumber = Utility.cleanPhonenumber(str)
            purifyMobileNumber = InputUtils.removedCountryCodeFromMobileNo(purifyMobileNumber)
            val findCountryCodeFromMobileNo = InputUtils.findCountryCodeFromMobileNo(str)
            val prop = PropBuilder()
            prop.put(AnalyticsConst.COUNTRY_CD, findCountryCodeFromMobileNo)
            prop.put(AnalyticsConst.NUMBER, purifyMobileNumber)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_PHONE_NUMBER_AUTO, prop)
            if (!findCountryCodeFromMobileNo.isNullOrBlank()) {
                binding.countryPicker.setCountryForPhoneCode(findCountryCodeFromMobileNo.substring(1).toInt())
            }
            if (!purifyMobileNumber.isNullOrBlank()) {
                binding.phoneET.setText(purifyMobileNumber)
                binding.phoneET.setSelection(purifyMobileNumber.length)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PHONE_SELECTED_FROM_HINT)
            }

        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            e.recordException()
        }
    }

    private fun showSessionEndedDialog() {
        var dialog: BukuAgenDialog? = null
        dialog = BukuAgenDialog(
            context = this,
            title = getString(R.string.session_ended),
            subTitle = getString(R.string.session_ended_description),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {},
            btnRightListener = {},
            btnLeftText = "",
            btnRightText = getString(R.string.login_again)
        )
        Utilities.showDialogIfActivityAlive(this, dialog)
    }
}
