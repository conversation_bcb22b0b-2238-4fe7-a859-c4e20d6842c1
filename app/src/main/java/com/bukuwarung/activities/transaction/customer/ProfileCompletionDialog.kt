package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.CompoundButton
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.NewVerifyOtpActivity
import com.bukuwarung.activities.referral.payment_referral.ReferralActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog.BusinessSelectedListener
import com.bukuwarung.dialogs.businessselector.BusinessType
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.databinding.ProfileCompletionDialogBinding
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.OnBoarding.shouldShowFullOnBoardingQuestion
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent
import net.yslibrary.android.keyboardvisibilityevent.util.UIUtil

class ProfileCompletionDialog(
        private val activity: AppCompatActivity,
        context: Context,
        private val action: (String) -> Unit,
        private val eventId: String,
        private val isBusinessTypeShown: Boolean,
        private val isSkipButtonShown: Boolean = true,
        private val handleValidation: Boolean = false,
        val customerId: String = "",
        val useParentBusinessHandler: Boolean = false
) : BaseDialog(context, BaseDialogType.POPUP), BusinessSelectedListener {

    private val binding by lazy {
        ProfileCompletionDialogBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    var bookEntity = BusinessRepository.getInstance(context).getBusinessByIdSync(User.getBusinessId())
    var dialog: BusinessSelectorDialog? = null
    private val customerType = mutableListOf<Int>()
    private val shouldShowFUllQuestion = RemoteConfigUtils.OnBoarding.shouldShowFullOnBoardingQuestion()

    private val checkBoxCheckedListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
        if (KeyboardVisibilityEvent.isKeyboardVisible(activity)){
            UIUtil.hideKeyboard(activity)
        }
        if (isChecked) {
            updateSellingMethodValidation(false)
        }
    }

    init {
        setCancellable(false)
        setUseFullWidth(false)
    }

    override fun getResId(): Int = 0


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (!isBusinessTypeShown) {
            binding.bizLayout.visibility = View.GONE
            binding.businessTypeError.visibility = View.GONE
        }

        if(useParentBusinessHandler){
            bookEntity = BusinessRepository.getInstance(activity).createBusiness(User.getUserId(), User.getDeviceId(), "BukuWarung", "Usaha Saya", -1, "")
        }

        val refCode = ReferralPrefManager.getInstance().temporaryReferralCode
        val businessTypes = AppConfigManager.getInstance().businessTypes
        var selectedBusiness: BusinessType? = BusinessType(bookEntity.bookType, bookEntity.bookTypeName)
        checkForErrorMessage()
//        binding.businessName.setText(bookEntity.businessName)
        binding.businessName.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE){
                binding.businessName.clearFocus()
                Utility.hideKeyboard(activity)
            }
            true
        }

        if (bookEntity != null && businessTypes.contains(selectedBusiness)) {
            val index: Int = businessTypes.indexOf(selectedBusiness)
            selectedBusiness = businessTypes.get(index)
            binding.businessTypeET.setText(selectedBusiness.getName())
        } else if (bookEntity != null) {
            binding.businessTypeET.setText(bookEntity.bookTypeName)
        }

        if (isBusinessTypeShown) {
            binding.ivTopPic.visibility = View.VISIBLE
            binding.clInfo.visibility = View.VISIBLE
        } else {
            binding.ivTopPic.visibility = View.GONE
            binding.clInfo.visibility = View.GONE
        }

        binding.businessTypeET.setOnClickListener { showBusinessTypeDlg(businessTypes, bookEntity.bookType); }

        binding.customerTypeEt.setOnClickListener { showCustomerTypeBottomSheet() }
        if (!isSkipButtonShown) {
            binding.buttonSkip.visibility = View.GONE
        }
        binding.buttonSkip.setOnClickListener {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.ID, eventId)
            if (customerId != "")
                propBuilder.put(AnalyticsConst.CUSTOMER_ID, customerId)
            dismissDialogAndShowShareOptions()
        }

        binding.businessName.doOnTextChanged { text, _, _, _ ->
            if (text?.isNotEmpty().isTrue){
                binding.businessNameLayout.error = ""
            }
        }

        binding.businessTypeET.doOnTextChanged { text, _, _, _ ->
            if (text?.isNotEmpty().isTrue){
                binding.bizLayout.error = ""
            }
        }

        binding.customerTypeEt.doOnTextChanged { text, _, _, _ ->
            if (text?.isNotEmpty().isTrue) {
                binding.customerLayout.error = ""
            }
        }

        binding.cbSellingOnline.setOnCheckedChangeListener(checkBoxCheckedListener)
        binding.cbSellingOffline.setOnCheckedChangeListener(checkBoxCheckedListener)

        binding.buttonSubmit.setOnClickListener {
            val bizName = binding.businessName.text.toString().replace("\\s", "")
            val formValidity = mutableListOf<Boolean>()

            val bizNameMissing = !Utility.hasBusinessName(bizName)
            binding.businessNameLayout.setErrorIf(!Utility.hasBusinessName(bizName), context.getString(R.string.biz_name_missing))
            formValidity.add(bizNameMissing)

            val isCategoryMissing = isBusinessTypeShown && (bookEntity.bookType == null || Utility.isBlank(binding.businessTypeET.text.toString()))
            binding.bizLayout.setErrorIf(isCategoryMissing, context.getString(R.string.biz_category_missing))
            formValidity.add(isCategoryMissing)

            val isCustomerTypeMissing = shouldShowFUllQuestion && customerType.isEmpty()
            binding.customerLayout.setErrorIf(isCustomerTypeMissing, context.getString(R.string.biz_customer_type_missing))
            formValidity.add(isCustomerTypeMissing)

            val isSellingMethodMissing = shouldShowFUllQuestion && !binding.cbSellingOnline.isChecked && !binding.cbSellingOffline.isChecked
            formValidity.add(isSellingMethodMissing)
            updateSellingMethodValidation(isSellingMethodMissing)

            if (formValidity.any { it }) return@setOnClickListener

            val sellingMethodForEvent = mutableListOf<String>().apply {
                if (binding.cbSellingOnline.isChecked) add(AnalyticsConst.ONLINE)
                if (binding.cbSellingOffline.isChecked) add(AnalyticsConst.OFFLINE)
            }

            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.BUSINESS_NAME, bizName)
            AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_NAME, bizName)
            if (customerId != "") {
                propBuilder.put(AnalyticsConst.CUSTOMER_ID, customerId)
            }
            if (!Utility.isBlank(eventId) && eventId.equals(AnalyticsConst.LANDING_POPUP)) {
                propBuilder.put(AnalyticsConst.TYPE, bookEntity.bookType.toString())
                if (shouldShowFUllQuestion) {
                    propBuilder.put(AnalyticsConst.MERCHANT_CUSTOMER_TYPE, binding.customerTypeEt.text.toString().toLowerCase())
                    propBuilder.put(AnalyticsConst.SELLING_METHOD, sellingMethodForEvent.joinToString())
                }

                if (!refCode.isNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.REFERRAL_CODE, refCode)
                    propBuilder.put("referral_input_method", "deeplink")
                    propBuilder.put("entered_referral_code", refCode)
                }

                AppAnalytics.trackEvent(AnalyticsConst.LANDING_POPUP, propBuilder)
            } else {
            }
            if(!useParentBusinessHandler) {
                BusinessRepository.getInstance(context).updateBusinessProfile(User.getUserId(), User.getDeviceId(), User.getBusinessId(), bizName, bookEntity.bookType, bookEntity.bookTypeName, bookEntity.businessOwnerName)
            }else{
                BusinessRepository.getInstance(context).createBusiness(User.getUserId(), User.getDeviceId(), bizName ?: "BukuWarung", bizName ?: "Usaha Saya", bookEntity.bookType, bookEntity.bookTypeName)
            }
            dismissDialogAndShowShareOptions()
            if (isBusinessTypeShown) {
                AppAnalytics.setUserProperty("business_type", bookEntity.bookType.toString())
                AppAnalytics.setUserProperty("business_type_name", bookEntity.bookTypeName)

                if (shouldShowFUllQuestion) {
                    AppAnalytics.setUserProperty(AnalyticsConst.MERCHANT_CUSTOMER_TYPE, binding.customerTypeEt.text.toString().toLowerCase())
                    AppAnalytics.setUserProperty(AnalyticsConst.SELLING_METHOD, sellingMethodForEvent.joinToString())
                }
            }

            // TODO submit the referral code
        }

        val referralCode = ReferralPrefManager.getInstance().referralCode
        Log.d("DeeplinkManager", referralCode.value + "receivedVal")
        referralCode.observe(activity, Observer {
            Log.d("DeeplinkManager", "visibility on" + it)
            binding.tilReferral.visibility = it.isNotEmpty().asVisibility()
            binding.tieReferral.setText(it)
        })


        binding.fullQuestionGroup.visibility = shouldShowFUllQuestion.asVisibility()
        KeyboardEventListener(activity) { isOpen ->
            if (!isOpen) {
                binding.businessName.clearFocus()
                binding.businessNameLayout.clearFocus()
            }
        }

        trackOnOpenEvent()
    }


    private fun trackOnOpenEvent(){
        val pageType =  if (shouldShowFullOnBoardingQuestion()) {
            AnalyticsConst.FOUR_QUESTIONS
        } else {
            AnalyticsConst.TWO_QUESTIONS
        }
        val prop = PropBuilder().put(AnalyticsConst.PAGE_TYPE, pageType)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_BUSINESS_DETAIL_DIALOG, prop)
    }

    private fun updateSellingMethodValidation(isInvalid: Boolean) {
        val color  = if (isInvalid)  ContextCompat.getColor(context, R.color.red_80) else ContextCompat.getColor(context, R.color.black_80)
        val helperText = if (isInvalid) context.getString(R.string.biz_selling_method_missing) else context.getString(R.string.mutliple_choice_allowed)

        binding.tvSellingMethod.setTextColor(color)
        binding.tvSellingMethodHelper.setTextColor(color)
        binding.tvSellingMethodHelper.text = helperText
    }

    private fun showCustomerTypeBottomSheet() {
        CustomerTypeBottomSheet(activity, customerType) { type ->
            customerType.clear()
            customerType.addAll(type)

            customerType.sortBy { it }
            val customerTypeStr = customerType.map {
                when (it) {
                    CustomerTypeBottomSheet.RESELLER -> context.getString(R.string.reseller)
                    CustomerTypeBottomSheet.DROPSHIPPER -> context.getString(R.string.dropshipper)
                    else -> context.getString(R.string.personal_use)
                }
            }

            binding.customerTypeEt.setText(customerTypeStr.joinToString())
        }.show()
    }

    public fun getBusinessDetails(): BookEntity {
        return bookEntity
    }

    private fun dismissDialogAndShowShareOptions() {
        InputUtils.hideKeyboard(context)
        
        val referralCode = binding.tieReferral.text.toString()
        if (referralCode.isNotNullOrEmpty()) {
            ReferralPrefManager.getInstance().temporaryReferralCode = referralCode
            dismiss()
            ReferralActivity.refCode = referralCode
//            activity.startActivity(Intent(activity, ReferralActivity::class.java))
            return
        }
        dismiss()
        if(useParentBusinessHandler && this.activity is NewVerifyOtpActivity){
            SetupManager.getInstance().hasRestored(true)
        }else {
            action(binding.businessName.text.toString().replace("\\s", ""))
        }
    }

    private fun checkForErrorMessage() {
        if (isBusinessTypeShown && bookEntity.bookType == -1 && handleValidation) {
            binding.businessTypeError.visibility = View.VISIBLE
        } else {
            binding.businessTypeError.visibility = View.GONE
        }
        val bizName = binding.businessName.text.toString()
        if (!Utility.hasBusinessName(bizName) && handleValidation) {
            binding.ownerNameError.visibility = View.VISIBLE
        } else {
            binding.ownerNameError.visibility = View.GONE
        }
    }

    private fun showBusinessTypeDlg(businessTypes: List<BusinessType?>?,
                                    bookType: Int) {
        dialog = BusinessSelectorDialog.getInstance(
                activity,
                businessTypes,
                this
        )
        if (bookType != 1) dialog?.selectedId = bookType
        dialog?.show()
    }

    override fun onBusinessSelected(datum: SelectableObject?) {
        bookEntity.bookType = datum!!.id
        bookEntity.bookTypeName = datum.name
        binding.businessTypeET.setText(datum.name)
        dialog?.dismiss()
        checkForErrorMessage()
    }

    override fun onNewBusinessAdded(datum: SelectableObject?) {
        bookEntity.bookType = datum!!.id
        bookEntity.bookTypeName = datum.name
        binding.businessTypeET.setText(datum.name)
        if (dialog != null) dialog!!.dismiss()
        checkForErrorMessage()
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN){
            val view = currentFocus ?: return super.dispatchTouchEvent(ev)
            if (view is EditText){
                val outRect = Rect()
                view.getGlobalVisibleRect(outRect)
                if (!outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())){
                    view.clearFocus()
                    Utility.hideKeyboard(activity)
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }
}