package com.bukuwarung.activities

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.location.Geocoder
import android.location.Location
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.provider.MediaStore
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.webkit.URLUtil
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.view.BusinessAddressActivity
import com.bukuwarung.activities.geolocation.view.MapsActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.livelinesscheck.CameraLivelinessActivity
import com.bukuwarung.activities.livelinesscheck.LivelinessLandingActivity
import com.bukuwarung.activities.onboarding.NewVerifyOtpActivity
import com.bukuwarung.activities.profile.businessprofile.CreateBusinessProfileActivity
import com.bukuwarung.activities.vida.VidaWebViewActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.PPOB_GAMING_VOUCHERS
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.APP_STATE_ALL_DELETED
import com.bukuwarung.constants.AppConst.LIVELINESS_FAIL_SUFFIX
import com.bukuwarung.constants.AppConst.LIVELINESS_SUCCESS_SUFFIX
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.referral.PaymentUserReferral
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.ReferralRepository.OnPaymentReferralDataCallback
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.dialogs.HelpDialog
import com.bukuwarung.databinding.ActivityWebviewBinding
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.lib.webview.camera.CameraKycActivity
import com.bukuwarung.lib.webview.data.OtpResponse
import com.bukuwarung.lib.webview.data.PrivyCredentials
import com.bukuwarung.lib.webview.util.Constant
import com.bukuwarung.lib.webview.util.FileSizeLimits
import com.bukuwarung.lib.webview.util.UploadsValidations
import com.bukuwarung.model.DeviceInfo
import com.bukuwarung.payments.CustomerListActivity
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.banklist.BankAccountListActivity
import com.bukuwarung.payments.bottomsheet.KycVerifyWarningBottomSheet
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.referral.usecase.ReferralUseCase
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.tutor.utils.Utils
import com.bukuwarung.ui.BukuAgenDialog
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.ShareUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utilities.launchUriIntent
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.await
import com.bukuwarung.utils.generateAndShareViewImage
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.openActivity
import com.bukuwarung.utils.orDefault
import com.bukuwarung.utils.saveAndShare
import com.bukuwarung.utils.tryToGetValueOrDefault
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.firebase.remoteconfig.ktx.get
import com.google.gson.Gson
import com.google.gson.JsonObject
import dagger.android.AndroidInjection
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.util.Locale
import java.util.UUID
import java.io.IOException
import java.util.*
import javax.inject.Inject


class WebviewActivity : BaseWebviewActivity(), OnPaymentReferralDataCallback,
    KycVerifyWarningBottomSheet.KycVerifyListener {
    private lateinit var binding: ActivityWebviewBinding
    private var showVideo = false
    private var tutorialVideoName: String? = ""
    private var entryPoint: String? = ""
    private var from: String? = null
    private var appealBankAccount: String? = null
    private val MWEB = "mweb"
    private val REDIRECTION_URL = "redirection_url"
    private val MAX_FILE_SIZE = 5 * 1024 * 1024
    private val RC_LOCATION_GRANTED_AFTER_DENY = 15
    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(applicationContext)
    }
    private val cancellationTokenSource = CancellationTokenSource()

    @Inject
    internal lateinit var viewModelFactory: ViewModelFactory<WebviewViewModel>
    private lateinit var viewModel: WebviewViewModel

    @Inject
    lateinit var referralUseCase: ReferralUseCase

    private val isKycRequired by lazy {
        intent?.getBooleanExtra(IS_KYC_REQUIRED, false) ?: false
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        AndroidInjection.inject(this)
        WebView.setWebContentsDebuggingEnabled(BuildConfig.DEBUG)
        viewModel = ViewModelProvider(this, viewModelFactory).get(WebviewViewModel::class.java)
        super.onCreate(savedInstanceState)

        binding = ActivityWebviewBinding.inflate(layoutInflater)
        if (intent.getBooleanExtra(WEBVIEW_PARAM_USE_REFERRAL, false)) {
            lifecycleScope.launch { referralUseCase.getReferralData() }
        }
        if (intent.hasExtra(SHOW_VIDEO_TUTORIAL)) {
            showVideo = intent.getBooleanExtra(SHOW_VIDEO_TUTORIAL, false)
        }
        if (intent.hasExtra(TUTORIAL_VIDEO_NAME)) {
            tutorialVideoName = intent.getStringExtra(TUTORIAL_VIDEO_NAME)
        }
        if (intent.hasExtra(ENTRY_POINT)) {
            entryPoint = intent.getStringExtra(ENTRY_POINT)
        }
        if (intent.hasExtra(APPEAL_BANK_ACCOUNT)) {
            appealBankAccount = intent.getStringExtra(APPEAL_BANK_ACCOUNT)
        }

        val propBuilder = PropBuilder()
        propBuilder.put(AnalyticsConst.LINK, getLink())
        propBuilder.put(AnalyticsConst.TITLE, getTitleText())
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_WEBVIEW, propBuilder)
        if (showVideo) {
            trackVideoLoadedEvent()
        }
        if(allowDebug()) {
            NotificationUtils.alertToast("webview url: " + getLink())
        }

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        if (intent?.hasExtra(PpobConst.CHECKOUT_TOKEN) == true) {
            postCallBackToWeb(intent?.getStringExtra(PpobConst.CHECKOUT_TOKEN))
        }
    }

    private fun postCallBackToWeb(checkoutToken: String?) {
        webView?.loadUrl("javascript:saldoPinCallback('$checkoutToken')")
    }

    private fun setupWebViewClient(userReferral: PaymentUserReferral) {
        val webViewClient: ReferralClient = ReferralClient(userReferral)
        if (webView != null) webView!!.webViewClient = webViewClient
    }

    override fun hideToolBar(): Boolean {
        return true
    }

    public override fun onResume() {
        super.onResume()
        val intent = intent
        if (intent.hasExtra("title") && intent.getStringExtra("title") == RemoteConfigUtils.getBukuSupplierTitle()) {
            webView?.loadUrl( "javascript:window.location.reload( true )" )
        }
    }

    override fun selectBankAccount(isForQris: Boolean, setQrisBank: Boolean) {
        val bookId = SessionManager.getInstance().businessId
        // NOTE: The way conditions written in BankAccountListActivity requires us
        // to send customerId, otherwise radio buttons won't function inside
        // BankAccountListAdapter.
        val i = BankAccountListActivity.createIntent(
            this,
            if (isForQris) "${PaymentConst.TYPE_QRIS_INT}" else "${PaymentConst.TYPE_PAYMENT_IN}",
            bookId, AnalyticsConst.MWEB,
            MWEB,
            setQrisBank = setQrisBank,
            settingFirstTime = true
        )
        startActivityForResult(i, RC_ADD_BANK_ACCOUNT)
    }


    override fun getAppVersion() {
        webView?.loadUrl("javascript:appVersionCallback('${BuildConfig.VERSION_NAME}')")
    }

    override fun onBackPressed() {
        super.onBackPressed()
    }

    override fun onCloseWebview() {
        if (!isKycRequired) super.onCloseWebview()
        else {
            KycVerifyWarningBottomSheet().show(supportFragmentManager, "verify-warning")
        }
    }

    override fun startLivelinessCheck(productId: String?) {
        startActivityForResult(
            LivelinessLandingActivity.createIntent(this, productId),
            RC_LIVELINESS )
    }

    override fun getLink(): String? {
        return intent.getStringExtra(LINK)
    }

    override fun allowDebug(): Boolean {
        return BuildConfig.DEBUG || BuildConfig.FLAVOR != "prod"
    }

    override fun getTitleText(): String? {
        return intent.getStringExtra(TITLE)
    }

    override fun getToolbarColor(): Int? {
        return R.color.colorPrimary
    }

    override fun getUserAgent(): String? {
        return null
    }

    override fun getDeeplinkScheme(): String? {
        return BuildConfig.DEEPLINK_SCHEME
    }

    override fun getAppToken(): String? {
        return SessionManager.getInstance().bukuwarungToken
    }

    override fun getJanusUrl(): String {
        return BuildConfig.API_BASE_URL_JANUS
    }

    override fun isAddressWithinVisitRange(address: String): Boolean {
        val addressJson: JsonObject? = Gson().fromJson(address, JsonObject::class.java)
        val foundCity = RemoteConfigUtils.physicalVisitCities().firstOrNull {
            addressJson?.get("city")?.asString?.lowercase() == it.lowercase()
        }
        return foundCity != null
    }

    override fun getAccountVerificationUrl(): String {
        return RemoteConfigUtils.getPaymentConfigs().kycWebUrl
    }

    override fun getAuthUrl(): String {
        return BuildConfig.API_BASE_URL
    }

    override fun getSessionToken(): String? {
        return SessionManager.getInstance().sessionToken
    }

    override fun getFileSizeLimits(): FileSizeLimits {
        return RemoteConfigUtils.getPaymentConfigs().fileSizeLimits ?: FileSizeLimits()
    }

    override fun getUploadValidations(): UploadsValidations {
        return RemoteConfigUtils.getPaymentConfigs().uploadsValidations ?: UploadsValidations()
    }

    override fun shouldApplyNewCompression(): Boolean {
        return RemoteConfigUtils.applyNewCompression()
    }

    override fun performImageValidations(): Boolean {
        return RemoteConfigUtils.getPaymentConfigs().performImageValidations.isTrue
    }

    override fun restartKyc() {
        webView?.loadUrl(
            "${RemoteConfigUtils.getPaymentConfigs().kycWebUrl}?entryPoint=$entryPoint"
        )
    }

    override fun getKycRedirectionData(): HashMap<String, String> {
        return RemoteConfigUtils.getPaymentConfigs().kycRedirectionData ?: hashMapOf()
    }

    override fun getPrivyCredentials(): PrivyCredentials? {
        return RemoteConfigUtils.getPaymentConfigs().privyCredentials
    }

    override fun getEnv(): String {
        return BuildConfig.FLAVOR
    }

    override fun getImageQuality(): Int {
        return RemoteConfigUtils.getPaymentConfigs().imageQuality.orDefault(Constant.IMAGE_QUALITY_DEFAULT)
    }

    override fun getVideoQuality(): String {
        return RemoteConfigUtils.getPaymentConfigs().videoQuality.orDefault(Constant.SD)
    }

    override fun getCompressionQuality(): String {
        return RemoteConfigUtils.getPaymentConfigs().compressionQuality.orDefault(Constant.LOW)
    }

    override fun getLuminosityCheck(): Boolean {
        return RemoteConfigUtils.getPaymentConfigs().shouldCheckLuminosity
    }

    override fun getMinLuminosity(): Float {
        return RemoteConfigUtils.getPaymentConfigs().minRequiredLuminosity
    }

    override fun getPhotoCompressionSize(): Float {
        return RemoteConfigUtils.getPaymentConfigs().photoCompressionSize
    }

    override fun webviewRefreshToken(): String? {
        return viewModel.refreshToken()
    }

    override fun getUserId(): String? {
        return SessionManager.getInstance().userId
    }

    override fun getAppsflyerId(): String? {
        return AppAnalytics.getAppsflyerId()
    }

    override fun getAppVersionCode(): String {
        return SetupManager.getInstance().installedVersion.toString()
    }

    override fun launchActivityForResult(requestType: String) {
        super.launchActivityForResult(requestType)
        when (requestType) {
            REQUEST_TYPE_ADDRESS -> startActivityForResult(
                BusinessAddressActivity.createIntent(this, true),
                RC_ADDRESS
            )
        }
    }

    override fun getWebviewUserUUID(): String? {
        var identity = SessionManager.getInstance().uuid
        if (TextUtils.isEmpty(identity)) {
            identity = User.getUserId()
        }
        return identity
    }

    override fun getBnplBookName(): String {
        var bnplBookName = SessionManager.getInstance().selectedBookName
        bnplBookName?.let {
            return bnplBookName
        }
        return "Not Found"
    }

    override fun isFeatureShown(id: String): String {
        if(id.isNullOrEmpty()) return false.toString();
        return OnboardingPrefManager.getInstance().getHasFinishedForId(id).toString();
    }

    override fun setFeatureShown(id: String) {
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
    }

    override fun getBnplBookId(): String {
        val bookEntity: BookEntity? = BusinessRepository.getInstance(this).bnplBookId
        return viewModel.fetchBnplBookName(bookEntity)
    }

    override fun getBWBookId(): String? {
        return User.getBusinessId()
    }

    override fun getBWBookName(): String? {
        return BusinessRepository.getInstance(this)
            .getBusinessByIdSync(User.getBusinessId()).businessName
    }

    override fun getSessionValueByKey(id: String): String? {
        // Will change this based on requirement
        return SessionManager.getInstance().getSessionValueByKey(id)
    }

    override fun getSharedPrefValue(id: String, type: String): String? {
        if(id == "session_manager"){
            return SessionManager.getInstance().getSessionValueByKey(type)
        }else if(id == "payment_manager"){
            return PaymentPrefManager.getInstance().getSessionValueByKey(type)
        }
        return super.getSharedPrefValue(id, type)
    }

    override fun onQrisSubmit(result: String, bookId: String) {
        super.onQrisSubmit(result, bookId)
        val bundle = Bundle()
        bundle.putBoolean(Constant.QRIS_SUBMITTED, true)
        bundle.putString(PaymentConst.QRIS_FORM_RESULT, result)
        bundle.putString(PaymentConst.QRIS_BOOK_ID, bookId)
        val tabName = TabName.PAYMENT
        MainActivity.startActivitySingleTopToTab(this, tabName, bundle)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            RC_ADDRESS -> {
                val prop = PropBuilder()
                if (resultCode != Activity.RESULT_OK) {
                    prop.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_QRIS_GEO_LOCATION, prop)
                    return
                }
                val address = data?.getParcelableExtra<Address?>(BusinessAddressActivity.ADDRESS)
                prop.put(
                    AnalyticsConst.STATUS,
                    if (address != null) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAIL
                )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_QRIS_GEO_LOCATION, prop)
                webView?.loadUrl("javascript:selectAddressCallback('${Gson().toJson(address)}')")
            }
            RC_CONTACT -> {
                webView?.loadUrl(
                    "javascript:contactCallback('${
                        data?.getStringExtra(
                            CustomerListActivity.PHONE_NUMBER
                        )
                    }')"
                )
            }
            RC_LIVELINESS -> {
                if (resultCode != Activity.RESULT_OK) return
                handleLivelinessResult(data)
            }
            RC_ADD_BANK_ACCOUNT -> {
                val bank = data?.getParcelableExtra(BankAccountListActivity.BANK_ACCOUNT) as? BankAccount
                webView?.loadUrl("javascript:selectBankCallback('${Gson().toJson(bank)}')")
            }
            RC_BUSINESS_NAME_CHANGE -> {
                val result = if (resultCode == Activity.RESULT_OK) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAIL
                webView?.loadUrl("javascript:businessNameUpdate('${result}')")
            }
            RC_OTP_VERIFICATION -> {
                if (resultCode == Activity.RESULT_OK) {
                    val otpResponse = OtpResponse(
                        data?.getStringExtra(NewVerifyOtpActivity.OTP_STATUS),
                        data?.getStringExtra(NewVerifyOtpActivity.OTP_TOKEN),
                    )
                    webView?.loadUrl("javascript:otpCallback('${Gson().toJson(otpResponse)}')")
                }
            }
            RC_LOCATION_GRANTED_AFTER_DENY -> {
                requestLocation()
            }
        }
    }

    private fun handleLivelinessResult(data: Intent?) {
        data ?: return
        val result = data.getBooleanExtra(CameraLivelinessActivity.RESULT,false)
        val url =
            AppConst.LIVELINESS_CALLBACK_URL +  if (result) LIVELINESS_SUCCESS_SUFFIX else LIVELINESS_FAIL_SUFFIX
        webView?.loadUrl(url)
    }

    override fun sendAppsflyerEvent(eventName: String, jsonProp: String?) {
        val prop = PropBuilder()
        try {
            if (jsonProp == null || jsonProp.isEmpty()) {
                AppAnalytics.trackAppsflyerEvent(eventName, prop)
                return
            }
            val jsonObject = JSONObject(jsonProp.trim { it <= ' ' })
            val keys = jsonObject.keys()
            while (keys.hasNext()) {
                val key = keys.next()
                if (jsonObject[key] is JSONObject) {
                    prop.put(key, jsonObject[key])
                }
            }
        } catch (e: Exception) {
            // do nothing
        }
        AppAnalytics.trackAppsflyerEvent(eventName, prop)
    }

    // TODO need to remove from library first
    override fun sendMoengageEvent(eventName: String, jsonProp: String?) {
        val prop = if (jsonProp.isNotNullOrEmpty()) {
            PropBuilder().stringToProp(jsonProp)
        } else {
            PropBuilder()
        }
        AppAnalytics.trackEvent(
            eventName, prop, false, true, false, false
        )
    }

    override fun trackEvent(
        eventName: String,
        jsonProp: String?,
        amplitude: Boolean,
        cleverTap: Boolean,
        firebase: Boolean,
        appsFlyer: Boolean,
        tiktokEventName: String?
    ) {
        val prop = if (jsonProp.isNotNullOrEmpty() && jsonProp != "undefined") {
            PropBuilder().stringToProp(jsonProp)
        } else {
            PropBuilder()
        }
        AppAnalytics.trackEvent(
            eventName, prop, amplitude, cleverTap, firebase, appsFlyer
        )
    }

    override fun onUserReferralDataLoaded(userReferral: PaymentUserReferral) {
        setupWebViewClient(userReferral)
    }

    override fun onSuccess(type: String?) {
        val bundle = Bundle()
        bundle.putString(Constant.EVENT_PROPS, eventProps)
        var tabName: TabName? = null
        when (type) {
            Constant.KYC -> {
                bundle.putBoolean(Constant.KYC_SUBMITTED, true)
                tabName = TabName.PAYMENT
            }
            Constant.KYB -> {
                bundle.putBoolean(Constant.KYB_SUBMITTED, true)
                tabName = TabName.PAYMENT
            }
            Constant.KYC_LENDING -> {
                bundle.putBoolean(Constant.KYC_SUBMITTED, true)
                tabName = TabName.OTHERS
            }
            Constant.QRIS -> {
                bundle.putBoolean(Constant.QRIS_SUBMITTED, true)
                tabName = TabName.PAYMENT
            }
            Constant.QRIS_BANK_SET_SELF, Constant.QRIS_BANK_SET_OTHER -> {
                val intent = Intent().apply {
                    putExtra(PaymentConst.QRIS_BANK_SET_FOR, type)
                }
                setResult(Activity.RESULT_OK, intent)
                finish()
                return
            }
            Constant.APPEAL_FLOW -> {
                bundle.putBoolean(PaymentConst.APPEAL_FLOW_SUBMITTED, true)
                tabName = TabName.PAYMENT
            }
        }
        MainActivity.startActivitySingleTopToTab(this, tabName, bundle)
        finish()
    }

    override fun onSuccessWithMessage(type: String?, message: String?) {
        when (type) {
            Constant.SALDO_TOP_UP -> {
                val bundle = Bundle()
                bundle.putString(PaymentConst.SUCCESS_TOP_MESSAGE, message)
                MainActivity.startActivitySingleTopToTab(this, TabName.PAYMENT, bundle)
                finish()
            }
        }
    }

    override fun redirectToPaymentDetail(orderId: String, paymentType: String, isSuccess: Boolean) {
        val intent = PaymentHistoryDetailsActivity.createIntent(
            this, "", orderId, paymentType,
            "", isFromAssistPage = true, isSuccess = isSuccess
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
        finish()
    }

    override fun redirectToDedicatedLoanBook() {
        finish()
        SessionManager.getInstance().businessId = getBnplBookId()
        SessionManager.getInstance().appState = APP_STATE_ALL_DELETED
        MainActivity.startActivityAndClearTopAndLoadWebView(this)
    }

    override fun redirectPaymentDetail(orderId: String, paymentType: String, isSuccess: Boolean, message: String, isFromAssistPage: Boolean) {
        val intent =  PaymentHistoryDetailsActivity.createIntent(
                this,
                "",
                orderId,
                paymentType, "", isFromAssistPage = isFromAssistPage, isSuccess = isSuccess, message = message
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
    }

    private inner class ReferralClient(private val userReferral: PaymentUserReferral) :
        WebViewClient() {
        override fun onPageFinished(view: WebView, url: String) {
            super.onPageFinished(view, url)

                NotificationUtils.alertToast("webview url"+url)

            val sessionManager = SessionManager.getInstance()
            val token = sessionManager.bukuwarungToken
            val userId = sessionManager.userId
            val tabIndex = intent.getIntExtra(WEBVIEW_PARAM_REFERRAL_TAB_INDEX, 0)
            var code: String? = ""
            if (!Utility.isBlank(userReferral.altRefCode)) {
                code = userReferral.altRefCode
            } else {
                finish()
            }

            // script to run "supplyToken(ACCESS_TOKEN, USER_ID, REFERRAL_ID, TAB_INDEX)"
            val script = StringBuilder("javascript:supplyToken(")
            script.append("'").append(token).append("'").append(",")
            script.append("'").append(userId).append("'").append(",")
            script.append("'").append(code).append("'").append(",")
            script.append(tabIndex).append(")")
            view.evaluateJavascript(script.toString(), null)
        }

        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.data = Uri.parse(url)
            finish()
            startActivity(intent)
            return true
        }
    }

    private fun trackVideoLoadedEvent() = PropBuilder().apply {
        put(AnalyticsConst.TUTORIAL_VIDEO_NAME, tutorialVideoName)
        put(AnalyticsConst.ENTRY_POINT2, entryPoint)
        AppAnalytics.trackEvent(AnalyticsConst.TUTORIAL_VIDEO_LOADED, this)
    }

    companion object {
        private const val REPLACE = "replace"
        const val REFERRAL_TAB_REFERRED = 0
        const val REFERRAL_TAB_REWARD = 1
        const val WEBVIEW_PARAM_USE_REFERRAL = "webview_param_use_referral"
        const val WEBVIEW_PARAM_REFERRAL_TAB_INDEX = "webview_param_referral_tab"
        const val APPEAL_BANK_ACCOUNT = "appeal_bank_account"
        private const val SHOW_VIDEO_TUTORIAL = "show_video_tutorial"
        private const val TUTORIAL_VIDEO_NAME = "tutorial_video_name"
        private const val ENTRY_POINT = "entry_point"
        private const val IS_KYC_REQUIRED = "is_kyc_required"
        private const val FROM = "from"
        private const val TYPE_PAYMENT_OUT = 1
        private const val CHECKOUT_TOKEN = "checkout_token"
        var context: Context? = null

        fun createIntent(origin: Context?, link: String?, title: String?) =
            Intent(origin, WebviewActivity::class.java).apply {
                context = origin
                val url = if (link?.contains(":businessId") == true) {
                    link?.replace(":businessId", SessionManager.getInstance().businessId)
                } else {
                    link
                }
                putExtra(LINK, url)
                putExtra(TITLE, title)
                if (title?.contains(PpobConst.CHECKOUT_TOKEN) == true) {
                    putExtra(CHECKOUT_TOKEN, title)
                }
            }

        fun createIntent(
            origin: Context?,
            link: String?,
            title: String?,
            isKycRequired: Boolean = false
        ): Intent {
            context = origin
            return Intent(origin, WebviewActivity::class.java).apply {
                putExtra(LINK, link)
                putExtra(TITLE, title)
                putExtra(IS_KYC_REQUIRED, isKycRequired)
            }
        }

        fun createIntent(
            origin: Context?,
            link: String?,
            title: String?,
            showVideoTutorial: Boolean,
            tutorialVideoName: String?,
            entryPoint: String?
        ): Intent {
            context = origin
            return Intent(origin, WebviewActivity::class.java).apply {
                putExtra(LINK, link)
                putExtra(TITLE, title)
                putExtra(SHOW_VIDEO_TUTORIAL, showVideoTutorial)
                putExtra(TUTORIAL_VIDEO_NAME, tutorialVideoName)
                putExtra(ENTRY_POINT, entryPoint)
            }
        }

        fun createIntent(origin: Context?, link: String?, title: String?, userAgent: String?): Intent {
            context = origin
            return Intent(origin, WebviewActivity::class.java).apply {
                putExtra(LINK, link)
                putExtra(TITLE, title)
                putExtra(USER_AGENT, userAgent)
            }
        }
    }

    override fun kycVerifyCancelClicked() {
        finish()
    }

    override fun openContactBook() {
        super.openContactBook()
        startActivityForResult(
            CustomerListActivity.createIntent(
                this,
                TYPE_PAYMENT_OUT,
                PPOB_GAMING_VOUCHERS,
                true
            ), RC_CONTACT
        )
    }

    override fun handleKycSuccess(kycSuccessFrom: KYC_SUCCESS) {
        when (kycSuccessFrom) {
            KYC_SUCCESS.BASIC_KYC ->
                webView?.loadUrl("${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?kycSubmitted=true")
            KYC_SUCCESS.BMU_RE_KYC ->
                webView?.loadUrl(RemoteConfigUtils.getPaymentConfigs().reKycRedirectUrl)
            KYC_SUCCESS.KYC_EDC_ORDER_DETAIL -> {
                val orderId = FeaturePrefManager.getInstance().edcOrderId
                openActivity(EdcOrderDetailsActivity::class.java){
                    putString(EdcOrderDetailsActivity.ORDER_ID, orderId)
                }
            }
            KYC_SUCCESS.BASIC_QRIS -> webView?.loadUrl(PaymentUtils.getQrisFormUrl())
            KYC_SUCCESS.ADDITIONAL_DOC_KYC -> webView?.loadUrl(RemoteConfigUtils.getPaymentConfigs().kycDocsUrl)
            KYC_SUCCESS.ADDITIONAL_DOC_QRIS -> {
                webView?.loadUrl("${RemoteConfigUtils.getPaymentConfigs().kycDocsUrl}?isQris=true")
            }
            KYC_SUCCESS.IS_LENDING -> {
                webView?.loadUrl(RemoteConfigUtils.getPaymentConfigs().lendingFormUrl)
            }
            KYC_SUCCESS.IS_BNPL -> {
                webView?.loadUrl(RemoteConfigUtils.getPaymentConfigs().bnplFormUrl)
            }
            KYC_SUCCESS.BASIC_QRIS_VIDEO -> {
                webView?.loadUrl(PaymentUtils.getQrisFormUrl())
            }
            KYC_SUCCESS.ADDITIONAL_DOC_KYC_VIDEO -> {
                webView?.loadUrl("${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?kycSubmitted=true")
            }
            KYC_SUCCESS.IS_BNPL_REGISTRATION_COMMERCE -> {
                webView?.loadUrl(RemoteConfigUtils.getPaymentConfigs().bnplFormUrl)
            }
            KYC_SUCCESS.IS_BNPL_REGISTRATION_PPOB -> {
                webView?.loadUrl(RemoteConfigUtils.getPaymentConfigs().ppobBnplFormUrl)
            }
        }
    }

    override fun handleKycSuccess(kycFlowConst: String?) {
        getKycRedirectionData()[kycFlowConst]?.let { webView?.loadUrl(it) }
    }

    override fun openCustomTab(url: String) {
        super.openCustomTab(url)
        val builder: CustomTabsIntent.Builder = CustomTabsIntent.Builder()
        builder.enableUrlBarHiding()
        builder.setToolbarColor(ContextCompat.getColor(this, R.color.colorPrimary))
        val customTabsIntent: CustomTabsIntent = builder.build()
        try {
            customTabsIntent.launchUrl(this, Uri.parse(url))
        } catch (e: Exception) {
            launchUriIntent(this, Uri.parse(url))
        }
    }

    override fun openBookUpdate(bookId: String) {
        super.openBookUpdate(bookId)
        val intent = CreateBusinessProfileActivity.createIntent(
            this, bookId, true, CreateBusinessProfileActivity.UseCase.QRIS
        )
        startActivityForResult(intent, RC_BUSINESS_NAME_CHANGE)
    }

    override fun openBookUpdateWithUseCase(bookId: String, useCase: String) {
        super.openBookUpdateWithUseCase(bookId, useCase)
        val useCaseEnum = when {
            useCase.equals(
                CreateBusinessProfileActivity.UseCase.QRIS.name,
                true
            ) -> CreateBusinessProfileActivity.UseCase.QRIS
            else -> CreateBusinessProfileActivity.UseCase.DEFAULT
        }
        val intent = CreateBusinessProfileActivity.createIntent(
            this, bookId, true, useCaseEnum
        )
        startActivityForResult(intent, RC_BUSINESS_NAME_CHANGE)
    }

    override fun requestLocation() {
        if (PermissonUtil.hasLocationPermission()) {
            fetchLocation()
        } else {
           PermissonUtil.requestLatLongPermission(this)
        }
    }

    private fun fetchLocation() {
        fusedLocationClient.getCurrentLocation(
            LocationRequest.PRIORITY_HIGH_ACCURACY,
            cancellationTokenSource.token
        )
            .addOnSuccessListener { location ->
                location?.let{
                    fetchLocationCallback(location)
                }
            }
            .addOnFailureListener {
                unableToFetchLocationCallback()
            }
    }
    private fun unableToFetchLocationCallback() {
        val locationCallbackJsonObject = JSONObject()
        locationCallbackJsonObject.put("message", "Unable to fetch location")
        locationCallbackJsonObject.put("success", false)
        val locationCallbackJsonString = locationCallbackJsonObject.toString()
        val javascriptCommand = "locationCallback($locationCallbackJsonString)"
        webView?.evaluateJavascript(javascriptCommand, null)
    }

    private fun fetchLocationCallback(location: Location) {
        if (!Utility.hasInternet()) {
            unableToFetchLocationCallback()
            return
        }
        val geocoder = Geocoder(applicationContext, Locale.getDefault())
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val addressList = withTimeout(5000L) {
                    geocoder.getFromLocation(location.latitude, location.longitude, 1)
                }
                withContext(Dispatchers.Main) {
                    if (!addressList.isNullOrEmpty()) {
                        val address = addressList[0]

                        // Extract address components
                        val city = address.locality // City
                        val state = address.adminArea // Province/State
                        val country = address.countryName // Country
                        val postalCode = address.postalCode // Postal Code
                        val premise = address.premises
                        val subLocality = address.subLocality
                        val subAdminArea = address.subThoroughfare
                        val subThoroughfare = address.subThoroughfare
                        val addressLine = address.getAddressLine(0) // Full Address

                        // Use the details as needed
                        Log.d("--->Address", "City: $city")
                        Log.d("--->Address", "State: $state")
                        Log.d("--->Address", "Country: $country")
                        Log.d("--->Address", "Postal Code: $postalCode")
                        Log.d("--->Address", "premise: $premise")
                        Log.d("--->Address", "subLocality: $subLocality")
                        Log.d("--->Address", "subAdminArea: $subAdminArea")
                        Log.d("--->Address", "subThoroughfare: $subThoroughfare")
                        Log.d("--->Address", "Full Address: $addressLine")

                        val locationCallbackJsonObject = JSONObject()
                        locationCallbackJsonObject.put("message", "location fetched")
                        locationCallbackJsonObject.put("success", true)
                        locationCallbackJsonObject.put("latitude", location.latitude)
                        locationCallbackJsonObject.put("longitude", location.longitude)
                        locationCallbackJsonObject.put("City", city)
                        locationCallbackJsonObject.put("State", state)
                        locationCallbackJsonObject.put("Country", country)
                        locationCallbackJsonObject.put("Postal Code", postalCode)
                        locationCallbackJsonObject.put("Full Address", addressLine)
                        locationCallbackJsonObject.put("premise", premise)
                        locationCallbackJsonObject.put("subLocality", subLocality)
                        locationCallbackJsonObject.put("subAdminArea", subAdminArea)
                        locationCallbackJsonObject.put("subThoroughfare", subThoroughfare)
                        val locationCallbackJsonString = locationCallbackJsonObject.toString()
                        val javascriptCommand = "locationCallback($locationCallbackJsonString)"
                        webView?.evaluateJavascript(javascriptCommand, null)
                    } else {
                        unableToFetchLocationCallback()
                        println("No address found for the provided coordinates.")
                    }
                }
            } catch (e: TimeoutCancellationException) {
                withContext(Dispatchers.Main) {
                    unableToFetchLocationCallback()
                    Log.e("Geocoder", "Timed out fetching address")
                }
            } catch (e: IOException) {
                withContext(Dispatchers.Main) {
                    unableToFetchLocationCallback()
                    println("Unable to get address from latitude and longitude")
                }
            }
        }
    }

    override fun fetchAddress(){
        var cityName: String? = null
        if (PermissonUtil.hasLocationPermission()) {
            runBlocking {
                val locationTask = fusedLocationClient.getCurrentLocation(
                    LocationRequest.PRIORITY_HIGH_ACCURACY,
                    cancellationTokenSource.token
                )

                locationTask.await()

                cityName = Geocoder(applicationContext, Locale.getDefault())
                    .getFromLocation(locationTask.result.latitude, locationTask.result.longitude, 1)
                    ?.get(0)?.subAdminArea
                val script = StringBuilder("javascript:fetchCityName(")
                script.append("'").append(cityName).append("'").append(")")
                runOnUiThread {
                    webView?.evaluateJavascript(script.toString(), null)
                }

            }

        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                requestPermissions(
                    arrayOf(
                        Manifest.permission.ACCESS_COARSE_LOCATION,
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ),
                    PermissionConst.ACCESS_LOCATION
                )
            }
        }

    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            PermissionConst.ACCESS_LOCATION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    val script = StringBuilder("javascript:fetchCityName(")
                    script.append("'").append("permission_granted").append("'").append(")")
                    runOnUiThread {
                        webView?.evaluateJavascript(script.toString(), null)
                    }
                } else if (grantResults.isEmpty()) {
                    // request cancelled
                    val script = StringBuilder("javascript:fetchCityName(")
                    script.append("'").append("permission_cancelled").append("'").append(")")
                    runOnUiThread {
                        webView?.evaluateJavascript(script.toString(), null)
                    }
                } else {
                    // Permission Denied
                    val script = StringBuilder("javascript:fetchCityName(")
                    script.append("'").append("permission_denied").append("'").append(")")
                    runOnUiThread {
                        webView?.evaluateJavascript(script.toString(), null)
                    }
                }
            }
            PermissionConst.WRITE_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    showFileChooser()
                }
            }
            PermissionConst.REQUEST_CODE_LAT_LONG -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    fetchLocation()
                } else {
                    requestLocationPermission()
                }
            }
        }
    }

    private fun requestLocationPermission() =
        if (ActivityCompat.shouldShowRequestPermissionRationale(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        ) {
            AlertDialog.Builder(this)
                .setTitle("Location permission required")
                .setMessage("This feature requires location permission")
                .setPositiveButton(
                    R.string.okay
                ) { _, _ ->
                    PermissonUtil.requestLatLongPermission(this)
                }
                .setNegativeButton(
                    R.string.batal
                ) { dialog, _ ->
                    unableToFetchLocationCallback()
                    dialog.dismiss()
                }
                .setCancelable(false)
                .create()
                .show()

        } else {
            AlertDialog.Builder(this)
                .setTitle("Permission Denied")
                .setMessage("This feature requires location permission. Please grant the permission in the app settings.")
                .setPositiveButton(
                    "Open Settings"
                ) { _, _ ->
                    val intent = Intent(
                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                        Uri.fromParts("package", packageName, null)
                    )
                    startActivityForResult(intent, RC_LOCATION_GRANTED_AFTER_DENY)
                }
                .setNegativeButton(
                    R.string.batal
                ) { dialog, _ ->
                    unableToFetchLocationCallback()
                    dialog.dismiss()
                }
                .setCancelable(false)
                .create()
                .show()
        }

    override fun selectQrisBankAccount(bookId: String) {
        // NOTE: The way conditions written in BankAccountListActivity requires us
        // to send customerId, otherwise radio buttons won't function inside
        // BankAccountListAdapter.
        val i = BankAccountListActivity.createIntent(
            this,
            "${PaymentConst.TYPE_QRIS_INT}",
            bookId, AnalyticsConst.MWEB,
            MWEB,
            setQrisBank = false,
            settingFirstTime = true
        )
        startActivityForResult(i, RC_ADD_BANK_ACCOUNT)
    }

    override fun addQrisBankAccount(bookId: String, addingFor: String, addingNewBank: Boolean) {
        startActivityForResult(
            AddBankAccountActivity.createIntent(
                this, paymentType =  "${PaymentConst.TYPE_QRIS_INT}",
                bookId = bookId, entryPoint = AnalyticsConst.MWEB,
                bankType = PaymentConst.QRIS_COMPATIBLE_BANKS_QUERY,
                settingFirstTime = addingNewBank,
                setQrisBank = !addingNewBank,
                addingBankAccountFor = PaymentConst.BankAccountOwner.valueOf(addingFor)
            ), RC_ADD_BANK_ACCOUNT
        )
    }

    override fun getPhoneNumber(): String? {
        return SessionManager.getInstance().countryCode + "-" + User.getUserId()
    }

    override fun trackEvent(eventName: String, eventProp: String) {
        if (eventProp.isNotNullOrEmpty()) {
            AppAnalytics.trackEvent(eventName, PropBuilder().stringToProp(eventProp))
        } else {
            AppAnalytics.trackEvent(eventName)
        }
    }

    override fun trackUserProperty(propName: String, propValue: String) {
        AppAnalytics.setUserProperty(propName, propValue)
    }

    override fun getBWAppToken(): String? {
        return SessionManager.getInstance().bukuwarungToken
    }

    override fun getBWUserId(): String? {
        return SessionManager.getInstance().userId
    }

    override fun getBWAppVersionName(): String? {
        return BuildConfig.VERSION_NAME
    }

    override fun isSaldoActivated(): String? {
        return PaymentPrefManager.getInstance().getHasActivatedSaldo().toString()
    }

    override fun getBWEntryPoint(): String? {
        return entryPoint
    }

    override fun openBWActivity(activity: String, parameter: String, title: String) {
        openActivity(activity, parameter, title)
    }

    override fun openDialog(
        title: String,
        message: String,
        positiveButtonType: String,
        negativeButtonType: String,
        activity: String,
        parameter: String
    ) {
        var dialog: BukuAgenDialog? = null
        dialog = BukuAgenDialog(
            context = this,
            title = title,
            subTitle = message,
            image = R.drawable.ic_transaction_failed,
            isLoader = false,
            btnLeftListener = {
                dialog?.dismiss()
                when (DialogButtonType.valueOf(negativeButtonType)) {
                    DialogButtonType.BACK -> onBackPressed()
                    DialogButtonType.CUSTOMER_SERVICE -> {
                        HelpDialog(this).show()
                    }
                    DialogButtonType.OPEN_ACTIVITY -> openActivity(activity, parameter, "")
                }

            },
            btnRightListener = {
                dialog?.dismiss()
                when (DialogButtonType.valueOf(positiveButtonType)) {
                    DialogButtonType.BACK -> onBackPressed()
                    DialogButtonType.CUSTOMER_SERVICE -> {
                        HelpDialog(this).show()
                    }
                    DialogButtonType.OPEN_ACTIVITY -> openActivity(activity, parameter, "")
                }
            },
            btnLeftText = if(negativeButtonType == DialogButtonType.CUSTOMER_SERVICE.name) "Hubungi CS" else "Kembali",
            btnRightText = if(positiveButtonType == DialogButtonType.CUSTOMER_SERVICE.name) "Hubungi CS" else "Kembali"
        )
        Utilities.showDialogIfActivityAlive(this, dialog)
    }

    private fun openActivity(activity: String, parameter: String, title: String) {
        var shouldFinish = false
        val intent = Intent(this, Class.forName(activity))
        if (!parameter.equals("undefined", true)) {
            val jsonObject = JSONObject(parameter)
            val keys = jsonObject.keys()
            while (keys.hasNext()) {
                val key = keys.next()
                if (key.equals(REPLACE, true)) {
                    shouldFinish = jsonObject.optBoolean(key)
                }
                if (key.equals(REDIRECTION_URL, true)) {
                    shouldFinish = true
                }
                intent.putExtra(key, jsonObject[key].toString())
            }
        }
        startActivity(intent)
        if (activity == "com.bukuwarung.activities.home.MainActivity"){
            finishAffinity()
        } else if (shouldFinish) {
            finish()
        }
    }

    override fun openWhatsappWithMessage(phoneNumber: String, message: String) {
        startActivity(Intent.createChooser(
                        ShareUtils.getShareUriOnWhatsappIntentWithPackage(
                                "com.whatsapp",
                                null, phoneNumber, message
                        ), "Choose an app")
        )
    }

    override fun fetchLocationAndImage(imageType: String) {
        if (RemoteConfigUtils.getPaymentConfigs().shouldEnableKybMapFlow) {
            startActivityForResult(
                MapsActivity.createIntent(this,
                    useCase = MapsActivity.UseCase.LOCATION_AND_IMAGE,
                    imageType = imageType,
                    mapConfig = RemoteConfigUtils.getPaymentConfigs().mapConfig),
                RC_LOCATION_AND_IMAGE)
        } else {
            startActivityForResult(CameraKycActivity.createIntent(this, imageType, useCase = CameraKycActivity.UseCase.IMAGE_AND_LOCATION), RC_LOCATION_AND_IMAGE)
        }
    }

    override fun copyToClipboard(text: String, toastText: String) {
        Utility.copyToClipboard(text,this,toastText)
    }

    override fun shareCalled() {
        saveAndShare(this, this.findViewById(R.id.webView), getString(R.string.share_with))
    }

    override fun getAppealBankAccount() {
        runOnUiThread{
            webView?.loadUrl("javascript:selectBankCallback('${appealBankAccount}')")
        }
    }

    override fun fetchRemoteConfig(key: String) : String? {
        val retVal = RemoteConfigUtils.remoteConfig[key]
        return retVal.asString()
    }

    override fun openHelpDialog() {
        HelpDialog(this).show()
    }

    override fun openBottomSheet(screenName: String, bottomSheetId: Int) {
        if (screenName.equals("ppob", true)) {
            if (bottomSheetId == 0) {
                val bundle = Bundle()
                bundle.putBoolean("openShowMore", true)
                MainActivity.startActivitySingleTopToTab(this, TabName.PAYMENT, bundle)
            }
        }
    }

    override fun shareWithOpenTray(message: String?, phoneNumber: String?) {
        val referralContents = RemoteConfigUtils.getReferralWebContent()
        val referralMessage = (referralContents.referral_share_text
            ?.replace("[link]", ReferralPrefManager.getInstance().referralDeeplink) ?: "-")
        RemoteConfigUtils.ReferralFloatingFeature.floatingButtonRedirectionType()
        Glide.with(this).load(referralContents.referral_share_image)
            .placeholder(R.drawable.wa_share_referral).into(binding.referralImagePreviewWeb)
        if (ShareUtils.isPackageInstalled("com.whatsapp", packageManager)) {
            generateAndShareViewImage(
                this, binding.referralImagePreviewWeb,
                referralMessage.replace("[input_referral_code]",
                    ReferralPrefManager.getInstance().myReferalCode) ?: "-",
                    phoneNumber,
                false,
                    from)
        } else {
            generateAndShareViewImage(
                this, binding.referralImagePreviewWeb,
                referralMessage.replace("[input_referral_code]",
                    ReferralPrefManager.getInstance().myReferalCode) ?: "-",
                null,
                false, from)
        }
    }

    override fun startOtpVerification(phoneNumber: String, countryCode: String, useCase: String) {
        super.startOtpVerification(phoneNumber, countryCode, useCase)
        val useCaseEnum = tryToGetValueOrDefault(
                { NewVerifyOtpActivity.Companion.UseCase.valueOf(useCase) },
                NewVerifyOtpActivity.Companion.UseCase.VERIFY_OTP_SALES_CODE
        )
        useCaseEnum?.let {
            val intent = NewVerifyOtpActivity.createIntent(
                    this,
                    phone = phoneNumber,
                    otpCode = "-",
                    countryCode = countryCode,
                    useCase = useCaseEnum
            )
            startActivityForResult(intent, RC_OTP_VERIFICATION)
        }
    }

    override fun shareOnWhatsapp(phoneNumber: String, message: String, url: String) {
        val tmpImg = ImageView(this)
        val progressDialog = ProgressDialog(this)
        var uriForImage: Uri? = null

        var bitmap: Bitmap? = null

        if (url.isNotNullOrEmpty() && URLUtil.isValidUrl(url)) {
            Glide.with(this)
                .asBitmap()
                .load(url)
                .into(object: SimpleTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        progressDialog.dismiss()
                        tmpImg.setImageBitmap(resource)
                        bitmap = resource
                    }

                    override fun onLoadStarted(placeholder: Drawable?) {
                        progressDialog.show()
                        super.onLoadStarted(placeholder)
                    }
                })

            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    requestPermissions(
                        arrayOf(
                            Manifest.permission.READ_MEDIA_IMAGES,
                        ), PermissionConst.WRITE_STORAGE
                    )
                } else {
                    requestPermissions(
                        arrayOf(
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        ),
                        PermissionConst.WRITE_STORAGE
                    )
                }
            } else {
                val handler = Handler()
                handler.postDelayed({
                    bitmap?.let {
                        uriForImage = getImageUri(tmpImg)
                        startActivity(
                            Intent.createChooser(
                                ShareUtils.getShareUriOnWhatsappIntentWithPackage(
                                    "com.whatsapp",
                                    uriForImage, phoneNumber, message
                                ), "Choose an app"
                            )
                        )
                        if (progressDialog.isShowing) {
                            progressDialog.dismiss()
                        }
                    }
                }, 1000)
            }

        } else {
            startActivity(
                Intent.createChooser(
                    ShareUtils.getShareUriOnWhatsappIntentWithPackage(
                        "com.whatsapp",
                        null, phoneNumber, message
                    ), "Choose an app"
                )
            )
        }
    }

    private fun getImageUri(view: ImageView?): Uri? {
        val bitmap = (view?.drawable as BitmapDrawable).bitmap
        val bytes = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bytes)
        val path: String = MediaStore.Images.Media.insertImage(
            this.contentResolver,
            bitmap,
            UUID.randomUUID().toString() + ".png",
            "drawing"
        )
        return Uri.parse(path)
    }

    override fun fetchDeviceDetails(): String? {
        val model = Build.MODEL
        val manufacturer = Build.MANUFACTURER
        val deviceId = User.getDeviceId()
        val androidId = tryToGetValueOrDefault(
            { Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID) },
            null
        )
        val info = DeviceInfo(model, manufacturer, deviceId, androidId)
        val gson = Gson()
        return gson.toJson(info)
    }

    override fun openWebview(url: String?) {
        url?.let {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            startActivity(browserIntent)
        }
    }

    override fun redirectVidaSign(url: String?) {
        url?.let {
            VidaWebViewActivity.openViewWebViewActivity(this, url)
            finish()
        }
    }

    enum class DialogButtonType {
        BACK,
        CUSTOMER_SERVICE,
        OPEN_ACTIVITY
    }
}
