package com.bukuwarung.activities.homepage.view

import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.ViewModelProviders
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.observe
import androidx.lifecycle.whenResumed
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.HomeBlueTopAdapter
import com.bukuwarung.activities.home.OnboardingBottomSheet
import com.bukuwarung.activities.home.ReferralErrorBottomSheet
import com.bukuwarung.activities.home.ReferralSuccessBottomSheet
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.data.FragmentBlock
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.homepage.data.FragmentData
import com.bukuwarung.activities.homepage.viewmodel.HomePageViewModel
import com.bukuwarung.activities.homepage.viewmodel.HomeTopFragmentViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.HOME_PAGE
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.constants.AppConst.USER_ALREADY_REFERRED
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.NgHomepageTopBlueSectionBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.constants.KycTier
import com.bukuwarung.payments.constants.QrisAndKycStatus
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.referral.model.ReferralDataPutRequest
import com.bukuwarung.referral.model.ReferralDataResponsePayload
import com.bukuwarung.referral.usecase.ReferralUseCase
import com.bukuwarung.session.SessionManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

class HomeTopBlueFragment : BaseFragment(), OnboardingWidget.OnboardingWidgetListener, Navigator {

    private lateinit var homepageTopBlueSectionBinding: NgHomepageTopBlueSectionBinding

    @Inject
    lateinit var viewModel: HomeTopFragmentViewModel

    @Inject
    lateinit var neuro: Neuro

    lateinit var homeViewModel: HomePageViewModel

    @Inject
    lateinit var referralUseCase: ReferralUseCase

    var category: String? = null
    var nextCoachmark: String? = null
    var nextSection: Int? = null

    private var fragmentData: FragmentData? = null

    private var bodyContents: FragmentBodyBlock? = null
    private var fragmentBlock: FragmentBlock? = null

    val scope = MainScope()

    companion object {
        private const val HOME_TOP_BLUE_FRAGMENT = "home_top_blue_fragment"
        private const val FRAGMENT_BLOCK = "fragment_block"

        fun createIntent(bukuTileContent: FragmentBodyBlock, fragmentBlock: FragmentBlock): HomeTopBlueFragment {
            val fragment = HomeTopBlueFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(HOME_TOP_BLUE_FRAGMENT, bukuTileContent)
                putParcelable(FRAGMENT_BLOCK, fragmentBlock)
            }

            return fragment
        }

    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        homepageTopBlueSectionBinding =
            NgHomepageTopBlueSectionBinding.inflate(layoutInflater, container, false)
        return homepageTopBlueSectionBinding.root
    }

    override fun setupView(view: View) {

        homepageTopBlueSectionBinding.apply {
            if (RemoteConfigUtils.showInfoAtTop()) {
                ivHelpIcon.showView()
            } else {
                ivHelpIcon.hideView()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if(ReferralPrefManager.getInstance().temporaryReferralCode.isNotNullOrEmpty() && ReferralPrefManager.getInstance().showSuccessReferralDialog){
            if(!ReferralPrefManager.getInstance().blockUserReferralFlow())checkReferrer()
        }
        else if(ReferralPrefManager.getInstance().showOnboardingDialog && RemoteConfigUtils.showOnboardingBottomsheet()){
            val bookName = SessionManager.getInstance().selectedBookName
            bookName?.let {
                OnboardingBottomSheet().show(
                    activity?.supportFragmentManager!!,
                    OnboardingBottomSheet.TAG
                )
                ReferralPrefManager.getInstance().showOnboardingDialog = false
            }
        }

        homeViewModel= activity?.run {
            ViewModelProviders.of(this).get(HomePageViewModel::class.java)
        } ?: throw Exception("Invalid Activity")

        bodyContents = arguments?.getParcelable(HOME_TOP_BLUE_FRAGMENT)
        fragmentBlock = arguments?.getParcelable(FRAGMENT_BLOCK)
        homepageTopBlueSectionBinding.kybUpgradeLayout.tvTickerBody.text =
            Utilities.makeSectionOfTextBold(
                RemoteConfigUtils.getPaymentConfigs().kybUpgradeInfo?.text.orEmpty(),
                RemoteConfigUtils.getPaymentConfigs().kybUpgradeInfo?.highlightedText.orEmpty(),
                object : ClickableSpan() {
                    override fun onClick(p0: View) {
                        //no imp needed.
                    }
                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = false
                        ds.typeface = Typeface.DEFAULT_BOLD
                        ds.color = requireContext().getColorCompat(R.color.black_80)
                    }
                }
            )

        bodyContents?.let {
            with(homepageTopBlueSectionBinding) {
                val showPaymentHeader = RemoteConfigUtils.NewHomePage.shouldShowPaymentHeader()
                tvSendBillText.visibility = showPaymentHeader.asVisibility()
                tvSendBillText.text = it.title

                val versionCode = BuildConfig.VERSION_CODE

                val finalItemList = it.data!!.filter{ it?.start_version!! <= versionCode && (it.end_version!! >= versionCode || it.end_version!! == -1) }.sortedBy { it?.rank }
                val homeBlueTopAdapter: HomeBlueTopAdapter
                layoutPaymentInOut.rvHomeBlueTop.isNestedScrollingEnabled = false
                layoutPaymentInOut.rvHomeBlueTop.apply {
                    category = fragmentBlock?.category
                    nextSection = fragmentBlock?.coachmark_next
                    homeBlueTopAdapter = HomeBlueTopAdapter(finalItemList, category) {
                            fragmentBody, category ->
                        run {
                            if(fragmentBody.check_kyc.isTrue){
                                if (PaymentPrefManager.getInstance()
                                        .getKycTier() == KycTier.NON_KYC
                                ) {
                                    homepageTopBlueSectionBinding.kybUpgradeLayout.root.hideView()
                                    val intent =
                                        HomepageNudgeActivity.createIntent(
                                            context,
                                            fragmentBody.display_name.toString(),
                                            "NON_KYC"
                                        )
                                    startActivity(intent)
                                } else if (PaymentPrefManager.getInstance()
                                        .getKycTier() == KycTier.ADVANCED && !PaymentUtils.isWhitelistedUser()
                                ) {
                                    val intent =
                                        HomepageNudgeActivity.createIntent(
                                            context,
                                            fragmentBody.display_name.toString(),
                                            "ADVANCED"
                                        )
                                    startActivity(intent)
                                } else {
                                    processRedirectAndAnalytics(fragmentBody, category)
                                }
                            } else {
                                processRedirectAndAnalytics(fragmentBody, category)
                            }
                        }
                    }
                    layoutManager = if (getNumberOfRows() == 1) {
                        LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)


                    } else {
                        GridLayoutManager(requireContext(), getNumberOfRows())
                    }
                    adapter = homeBlueTopAdapter
                }
                with(homepageTopBlueSectionBinding.layoutLearnAccountLevels.ivIcon) {
                    homepageTopBlueSectionBinding.layoutLearnAccountLevels.root.showView()
                    when (PaymentPrefManager.getInstance().getKycTier()) {
                        KycTier.NON_KYC -> {
                            setImageResource(R.drawable.ic_kyc_badge_standard)
                            homepageTopBlueSectionBinding.layoutLearnAccountLevels.tvType.text =
                                getString(R.string.standard)
                            homepageTopBlueSectionBinding.kybUpgradeLayout.root.hideView()
                        }
                        KycTier.ADVANCED -> {
                            setImageResource(R.drawable.ic_kyc_badge_premium)
                            homepageTopBlueSectionBinding.layoutLearnAccountLevels.tvType.text =
                                getString(R.string.premium)
                            if (PaymentUtils.isWhitelistedUser()) {
                                homepageTopBlueSectionBinding.kybUpgradeLayout.root.showView()
                            }
                        }
                        KycTier.SUPREME -> {
                            setImageResource(R.drawable.ic_kyc_badge_priority)
                            homepageTopBlueSectionBinding.layoutLearnAccountLevels.tvType.text =
                                getString(R.string.prioritas)
                            homepageTopBlueSectionBinding.kybUpgradeLayout.root.hideView()
                        }
                    }
                }

                PaymentPrefManager.getInstance().flagUpdated.observe(viewLifecycleOwner) {
                    if (it) homeBlueTopAdapter.notifyDataSetChanged()
                }

                homepageTopBlueSectionBinding.tvKycPromptText.hideView()

                /*
                    Refresh payments options only if whitelist limits are received.
                 */
                homepageTopBlueSectionBinding.layoutLearnAccountLevels.ivRight.setOnClickListener {
                    redirectToLearnAboutAccountLevels()
                    val propBuilder = AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ENTRY_POINT2, HOME_PAGE)
                        put("link", RemoteConfigUtils.getLearnAboutAccountLevelsLink())
                        put("kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString())
                    }
                    AppAnalytics.trackEvent("kyc:learn_kyc_clicked", propBuilder)
                }
                homepageTopBlueSectionBinding.layoutLearnAccountLevels.tvLearn.setOnClickListener {
                    redirectToLearnAboutAccountLevels()
                    val propBuilder = AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ENTRY_POINT2, HOME_PAGE)
                        put("link", RemoteConfigUtils.getLearnAboutAccountLevelsLink())
                        put("kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString())
                    }
                    AppAnalytics.trackEvent("kyc:learn_kyc_clicked", propBuilder)
                }
                homepageTopBlueSectionBinding.kybUpgradeLayout.bukuButton.apply {
                    setOnClickListener {
                        PaymentUtils.showKycKybStatusBottomSheet(parentFragmentManager, HOME_PAGE)
                        val propBuilder = AppAnalytics.PropBuilder().apply {
                            put(ENTRY_POINT2, HOME_PAGE)
                            put(
                                "kyc_tier", PaymentPrefManager.getInstance()
                                    .getKycTier().toString()
                            )
                        }
                        AppAnalytics.trackEvent("kyb_view_page", propBuilder)
                    }
                }

                showPaymentPrompt()

                PaymentPrefManager.getInstance().paymentLimits.observe(viewLifecycleOwner) {
                    if (it != null) {
                        homeBlueTopAdapter.notifyDataSetChanged()
                        showPaymentPrompt()
                    }
                }

                PaymentPrefManager.getInstance().qrisData.observe(viewLifecycleOwner) {
                    val bookId = SessionManager.getInstance().businessId
                    val qrisBookId = it.qrisBookId
                    if ((qrisBookId.isNotNullOrEmpty() && it.finalStatus.isNotNullOrEmpty() &&
                                it.finalStatus != QrisAndKycStatus.VERIFIED.name &&
                                qrisBookId == bookId) ||
                        (it.finalStatus == QrisAndKycStatus.VERIFIED.name && it.qrisBankId.isNullOrEmpty())
                    ) {
                        if (it.isApprovedQrisUser()) {
                            return@observe
                        }
                        homepageTopBlueSectionBinding.incCtaView.root.showView()

                        val qrisStatusText: String
                        val textToBold: String
                        var boldTextColor = R.color.black_80
                        when {
                            (it.finalStatus?.equals(QrisAndKycStatus.REJECTED.name, true).isTrue) -> {
                                qrisStatusText = getString(R.string.qris_rejected_verify_again)
                                textToBold =
                                    getString(R.string.qris_rejected_verify_again_bold_substring)
                            }
                            (it.finalStatus?.equals(QrisAndKycStatus.VERIFIED.name, true).isTrue && it.qrisBankId.isNullOrEmpty()) -> {
                                qrisStatusText = getString(R.string.qris_setup_bank_here)
                                textToBold = getString(R.string.qris_setup_bank_here_bold_substring)
                                boldTextColor = R.color.blue_60
                            }
                            else -> {
                                qrisStatusText = getString(R.string.qris_is_processed_more)
                                textToBold =
                                    getString(R.string.qris_is_processed_more_bold_substring)
                            }
                        }
                        homepageTopBlueSectionBinding.incCtaView.tvQrisStatus.text =
                            Utilities.makeSectionOfTextBold(
                                qrisStatusText,
                                textToBold,
                                object : ClickableSpan() {
                                    override fun onClick(widget: View) {
                                        PaymentUtils.handlePaymentsRedirection(
                                            requireContext(),
                                            childFragmentManager,
                                            AnalyticsConst.HomePage.HOMEPAGE,
                                            PaymentConst.QRIS_HANDLING_KEY,
                                            AnalyticsConst.QRIS_STATUS
                                        )
                                    }

                                    override fun updateDrawState(ds: TextPaint) {
                                        super.updateDrawState(ds)
                                        ds.isUnderlineText = false
                                        ds.typeface = Typeface.DEFAULT_BOLD
                                        ds.color = requireContext().getColorCompat(boldTextColor)
                                    }
                                })
                        homepageTopBlueSectionBinding.incCtaView.tvQrisStatus.setOnClickListener {
                            PaymentUtils.handlePaymentsRedirection(
                                requireContext(), childFragmentManager,
                                AnalyticsConst.HomePage.HOMEPAGE, PaymentConst.QRIS_HANDLING_KEY,
                                AnalyticsConst.QRIS_STATUS
                            )
                        }
                    } else {
                        homepageTopBlueSectionBinding.incCtaView.root.hideView()
                    }
                }
            }
        }

        homepageTopBlueSectionBinding.ivHelpIcon.setSingleClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.HOME_TUTORIAL_OPEN)
            showCoachmark(fragmentBlock)
        }
    }

    private fun showPaymentPrompt() {
        homepageTopBlueSectionBinding.tvKycPromptText.showView()

        val paymentOutLimit = PaymentPrefManager.getInstance().getPaymentLimits()?.whitelistLimits?.paymentOutLimits?.remainingTrxAmountLimit.orNil

        homepageTopBlueSectionBinding.tvKycPromptText.text = RemoteConfigUtils.getPaymentConfigs().whitelistedUserLimitInfo

        if (paymentOutLimit <= 0) {
            homepageTopBlueSectionBinding.tvKycPromptText.hideView()
        }

        if (PaymentPrefManager.getInstance().getKycTier() == KycTier.ADVANCED &&
            PaymentUtils.isWhitelistedUser()
        ) {
            homepageTopBlueSectionBinding.kybUpgradeLayout.root.showView()
        } else {
            homepageTopBlueSectionBinding.kybUpgradeLayout.root.hideView()
        }
    }

    private fun redirectToLearnAboutAccountLevels() {
        val deeplink = RemoteConfigUtils.getLearnAboutAccountLevelsLink()
        if (deeplink.isNotNullOrEmpty()) {
            if (Utility.hasInternet()) {
                startActivity(
                    WebviewActivity.createIntent(
                        requireActivity(), deeplink,
                        "BukuWarung"
                    )
                )
            } else {
                NoInternetAvailableDialog.show(childFragmentManager)
            }
        }
    }

    private fun initCoachMark() {
        if(ReferralPrefManager.getInstance().showOnboardingDialog && RemoteConfigUtils.showOnboardingBottomsheet()){
            val bookName = SessionManager.getInstance().selectedBookName
            bookName?.let {
                OnboardingBottomSheet().show(
                    activity?.supportFragmentManager!!,
                    OnboardingBottomSheet.TAG
                )
                ReferralPrefManager.getInstance().showOnboardingDialog = false
            }
        }
        if (!FeaturePrefManager.getInstance().isHomeCoachmarkSeen) {
            if (SessionManager.getInstance().isExistingOldUser
                && RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()
            ) {
                showInitialDialog()
            } else {
                showCoachmark(fragmentBlock)
            }
            FeaturePrefManager.getInstance().setHomeCoachMarkSeen()
        }
    }

    private fun checkReferrer() {
        lifecycleScope.launch {
            if (ReferralPrefManager.getInstance().temporaryReferralCode.isNotNullOrEmpty() && ReferralPrefManager.getInstance().temporaryReferralCode != ReferralPrefManager.getInstance().myReferalCode) {
                val getResponse = referralUseCase.getReferralData()
                if (getResponse != null) {
                    val referralCode = getResponse.referredByCode
                    ReferralPrefManager.getInstance().myReferalCode = getResponse.userReferralCode
                    if(referralCode.isNotNullOrEmpty()){
                        // User already have a referrer
                        ReferralPrefManager.getInstance().blockUserReferralFlow(true)
                    }
                    else{
                        //Register referral code to server
                        whenResumed {
                            sendToServer(ReferralPrefManager.getInstance().temporaryReferralCode)
                        }
                    }
                } else {
                    whenResumed {
                        ReferralErrorBottomSheet().show(
                            activity?.supportFragmentManager!!,
                            ReferralErrorBottomSheet.TAG
                        )
                    }
                }
            }
        }
    }

    // Register referral code to server
    fun sendToServer(referredByCode : String) {
        if (ReferralPrefManager.getInstance().paymentReferralInUse.isEmpty()) {
            if (referredByCode.isNotNullOrEmpty() && referredByCode != ReferralPrefManager.getInstance().myReferalCode) {
                val obj = ReferralDataPutRequest(referredByCode)
                var putResponse: ReferralDataResponsePayload?
                lifecycleScope.launch {
                    putResponse = referralUseCase.putReferralData(obj)
                    if (putResponse != null && !(putResponse as ReferralDataResponsePayload).error) {
                        ReferralSuccessBottomSheet { initCoachMark() }.show(
                            activity?.supportFragmentManager!!,
                            ReferralSuccessBottomSheet.TAG
                        )
                        val prop = AppAnalytics.PropBuilder()
                        prop.put(AnalyticsConst.INPUTTED_REFERRAL_CODE, ReferralPrefManager.getInstance().temporaryReferralCode)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFEREE_ONBOARD_APPEARED, prop)

                        ReferralPrefManager.getInstance().paymentReferralInUse =
                            ReferralPrefManager.getInstance().temporaryReferralCode
                        ReferralPrefManager.getInstance().showSuccessReferralDialog=false

                        val prop2 = AppAnalytics.PropBuilder()
                        prop2.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.REFERRAL_SUCCESS_BOTTOMSHEET)
                        prop2.put("referral_code_input_method", AnalyticsConst.DEEPLINK)
                        prop2.put(AnalyticsConst.ENTERED_REFERRAL_CODE, ReferralPrefManager.getInstance().temporaryReferralCode)
                        prop2.put("referral_code_input_status", AnalyticsConst.CODE_ACCEPTED)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_REFERRAL_CODE, prop2)

                        ReferralPrefManager.getInstance().paymentReferralInUse =
                            putResponse!!.referredByCode
                        ReferralPrefManager.getInstance().referralDeeplink =
                            putResponse!!.userReferralDeeplink

                        val prop3 = AppAnalytics.PropBuilder()
                        prop3.put(AnalyticsConst.INPUTTED_REFERRAL_CODE, ReferralPrefManager.getInstance().temporaryReferralCode)
                        prop3.put(AnalyticsConst.REFERRER_ID, putResponse?.referredByUserId)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFEREE_ONBOARD_CONFIRMED, prop3)
                    }else if (putResponse != null && (putResponse as ReferralDataResponsePayload).error){
                        if((putResponse as ReferralDataResponsePayload).errorTypeCode == USER_ALREADY_REFERRED){
                            ReferralPrefManager.getInstance().blockUserReferralFlow(true)
                        }
                    }else{
                        ReferralErrorBottomSheet().show(
                            activity?.supportFragmentManager!!,
                            ReferralErrorBottomSheet.TAG
                        )
                    }

                    val prop2 = AppAnalytics.PropBuilder()
                    prop2.put("activation_type", "automatic")
                    prop2.put("status", if (putResponse != null) "success" else "fail")
                    prop2.put("referrer_code", referredByCode)
                    prop2.put("error_reason", if (putResponse != null) "" else "empty")
                    AppAnalytics.trackEvent("referral_activation_response",prop2)
                }
            }
        }
    }

    fun showCoachmark(fragmentData: FragmentBlock?) {
        fragmentData?.let {

            if (isAdded && activity != null && !requireActivity().isFinishing) {
                scope.launch {
                    delay(200)
                    OnboardingWidget.createInstance(
                        requireActivity(),
                        this@HomeTopBlueFragment,
                        fragmentData.analytics_block_name,
                        homepageTopBlueSectionBinding.vwCoachmark,
                        null,
                        fragmentData.coachmark_header!!,
                        fragmentData.coachmark_body!!,
                        getString(R.string.next),
                        FocusGravity.CENTER,
                        ShapeType.RECTANGLE_FULL,
                        fragmentData?.coachmark_step!!,
                        fragmentData.coachmark_max_steps!!,
                        sendAnalytics = true,
                        sendAnalyticsOnDismiss = true,
                        delay = 5,
                        isSnackBarClick = false,
                        isPerformClick = true
                    )
                }
            }
        }
    }

    private fun getNumberOfRows(): Int {
        val displayMetrics: DisplayMetrics = requireContext().resources.displayMetrics
        val dpWidth: Float = displayMetrics.widthPixels / displayMetrics.density
        return (dpWidth / 120).toInt()
    }

    private fun processRedirectAndAnalytics(fragmentBody: BodyBlock, category: String?) {
        // Redirection/ deeplink/ flow

        val redirection = fragmentBody.deeplink_app!!
        val deeplink = fragmentBody.deeplink_web!!
        if (fragmentBody.deeplinkAppNeuro.isNotNullOrBlank()) {
          redirect(fragmentBody.deeplinkAppNeuro.orEmpty())
        } else if (fragmentBody.redirection_handler.isNotNullOrBlank()) {
            when (fragmentBody.redirection_handler) {
                PaymentConst.PAYMENT_HP_HANDLER -> context?.let {
                    PaymentUtils.handlePaymentsRedirection(
                        it, childFragmentManager,
                        AnalyticsConst.HomePage.HOMEPAGE,
                        fragmentBody.analytics_name,
                        null
                    )
                }
            }
        } else {
            if (fragmentBody.coming_soon) {
                Toast.makeText(requireContext(), "Fitur ini segera datang", Toast.LENGTH_SHORT).show()
            }

            if (redirection.isNotNullOrEmpty()) {
                redirect(redirection)
            }
            if (deeplink.isNotNullOrEmpty()) {
                if (Utility.hasInternet()) {
                    startActivity(
                        WebviewActivity.createIntent(
                            requireActivity(), deeplink,
                            fragmentBody.display_name
                        )
                    )
                } else {
                    NoInternetAvailableDialog.show(childFragmentManager)
                }
            }
        }
        handleAnalytics(fragmentBody, category)

    }

    private fun redirect(redirection: String) {
        val sourceLink = SourceLink(context = requireContext(), link = redirection)

        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(redirection) },
        )
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$redirection&from=home&launch=2"
        val sourceLink = SourceLink(context = requireContext(), link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    private fun handleAnalytics(fragmentBody: BodyBlock, category: String?) {
        val prop = AppAnalytics.PropBuilder()

        prop.put(AnalyticsConst.HOMEPAGE_SECTION, category)
        prop.put(AnalyticsConst.HomePage.BUTTON_NAME, fragmentBody.analytics_name)
        prop.put(AnalyticsConst.HomePage.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)

        AppAnalytics.trackEvent(AnalyticsConst.HomePage.EVENT_HOMEPAGE_BUTTON_CLICK, prop)
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {


        }
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
       // No imp needed
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        homeViewModel.onEventReceived(HomePageViewModel.Event.OnNextCoachmarkClick(nextSection))
    }

    private fun showInitialDialog() {
        AppAnalytics.trackEvent(AnalyticsConst.HOMEPAGE_INTRO_BANNER_APPEAR)
        lifecycleScope.launchWhenResumed {
            context?.let {
                WelcomeDialog(it, callback = {
                    showCoachmark(fragmentData = fragmentBlock)
                }).show()
            }
        }
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
}