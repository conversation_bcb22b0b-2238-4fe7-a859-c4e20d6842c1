package com.bukuwarung.activities.inventory.detail

import android.content.DialogInterface
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.method.DigitsKeyListener
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.TextView
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.databinding.BottomSheetDialogEditStockBinding
import com.bukuwarung.inventory.dialog.CustomMeasurementUnitDialog
import com.bukuwarung.inventory.generateId
import com.bukuwarung.inventory.ui.measurement.StockUnitBottomSheet
import com.bukuwarung.utils.*
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip



class EditStockBottomSheetFragment : BottomSheetDialogFragment(), StockUnitBottomSheet.StockUnitSelection, CustomMeasurementUnitDialog.AddMeasurementInterface {

    internal lateinit var viewModel: InventoryHistoryDetailViewModel
    private lateinit var productId: String
    private lateinit var produtEntity: ProductEntity
    private lateinit var mBinding: BottomSheetDialogEditStockBinding
    private var customMeasurementUnitDialog: CustomMeasurementUnitDialog? = null
    private var tooltip: SimpleTooltip? = null
    private val UNIT_NAME_LENGTH = 8
    private lateinit var oldStockValue: String
    private lateinit var sellingPriceEdit: TextView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (arguments != null) {
            if (requireArguments().containsKey(PRODUCT_ID)) {
                productId = requireArguments().getString(PRODUCT_ID).toString()

            }
        }
        val factory = InventoryHistoryDetailViewModelFactory(this, InventoryRepository.getInstance(activity),
                ProductRepository.getInstance(activity),
                productId)

        this.viewModel = ViewModelProviders.of(this, factory)
                .get(InventoryHistoryDetailViewModel::class.java)

        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialogThemeRound)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        mBinding = BottomSheetDialogEditStockBinding.inflate(inflater, container, false)
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        initKeyboard()
        mBinding.ivClose.setOnClickListener { v: View? ->
            InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
            if (dialog != null) dialog!!.dismiss()
        }

        sellingPriceEdit = mBinding.sellingPriceEdit
        mBinding.sellingPriceParent.setOnClickListener {
            changeCalculatorFocusToTrx()
        }
        mBinding.numberStepperLayout.btnIncrease.setOnClickListener { v: View? ->
            try {
                InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
                mBinding.numberStepperLayout.etNumber.clearFocus()
                var olderValue: Int
                if (mBinding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                    olderValue = 0
                } else {
                    olderValue = mBinding.numberStepperLayout.etNumber.text.toString().toInt()
                }

                olderValue++
                mBinding.numberStepperLayout.etNumber.setText(olderValue.toString())
            } catch (e: Exception) {
                // raise the exception here.
                Log.v("exception here", e.message.toString())
            }
        }
        mBinding.numberStepperLayout.btnDecrease.setOnClickListener { v: View? ->
            try {
                InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
                mBinding.numberStepperLayout.etNumber.clearFocus()
                var olderValue: Int
                if (mBinding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                    olderValue = 0
                } else {
                    olderValue = mBinding.numberStepperLayout.etNumber.text.toString().toInt()
                }
                olderValue--
                if (olderValue >= 0) mBinding.numberStepperLayout.etNumber.setText(olderValue.toString())
            } catch (e: Exception) {
                // raise the exception here.
            }
        }

        mBinding.numberStepperLayout.etNumber.keyListener =
            DigitsKeyListener.getInstance("0123456789")

        mBinding.stockMinimu.setOnClickListener {

            context?.also {
                val tooltipBuilder = TooltipBuilder.builder(it)
                        .setAnchor(mBinding.stockMinimu)
                        .setText("Kamu akan mendapatkan \n" +
                                "pengingat ketika stok menipis")
                        .setGravity(Gravity.LEFT)
                tooltip = tooltipBuilder.build()
                tooltip?.show()
            }
        }
        mBinding.sellingPriceEdit.doOnTextChanged { text, start, count, after ->
            viewModel.productPriceChange(text.toString())
        }
        mBinding.productNameET.setOnFocusChangeListener { v, hasFocus ->
            if(hasFocus) {
                closeCalculatorKeyboard()
            }
        }
        mBinding.numberStepperLayout.etNumber.setOnFocusChangeListener { v, hasFocus ->
            if(hasFocus) {
                closeCalculatorKeyboard()
            }
        }
        clearFocus()
        changeCalculatorFocusToTrx()
        viewModel.init(arguments?.getBoolean(TRANSACTION_FLOW) ?: false)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {

            val dialog = it as BottomSheetDialog
            val bottomSheet = dialog.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }

        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        observeData()
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
        viewModel.observerEditProductState.observe(requireActivity(), Observer {
            when(it) {
                is InventoryHistoryDetailViewModel.EditProductState.ShowInputUI -> {
                    mBinding.StockMinimumLayout.visibility = (!it.hideMinMax).asVisibility()
                }
            }

        } )
    }

    /**
     * Show data based on state.
     */
    private fun showBasedOnState(state: StockDetailState) {
        when (state.currentProductState) {
            ProductDetailStateType.Loading -> {

            }
            ProductDetailStateType.NotFound -> {


                //TODO - we can add conition here.

                /*   handler.postDelayed({
                       finish()
                   }, 1000)*/
            }
            ProductDetailStateType.Loaded -> {

                state.productEntity?.let {
                    mBinding.item = it
                    produtEntity = it
                    oldStockValue = "" + it.minimumStock
                    mBinding.numberStepperLayout.etNumber.setText("" + it.minimumStock)
                    mBinding.numberStepperLayout.etNumber.isEnabled = true
                    val price = Utility.priceToString(it.unitPrice, false)
                    mBinding.sellingPriceEdit.text = price
                    mBinding.textAmountCalc.text = Utility.formatCurrencyForEditing(it.unitPrice)
                }

                mBinding.btnConfirm.setOnClickListener { v: View? ->
                    try {
                        InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
                        val newMinValue = mBinding.numberStepperLayout.etNumber.text.toString().toInt()
                        var newSellingPrice = 0.0
                        val bal = mBinding.sellingPriceEdit.text.toString().trim()
                        if (bal.isNotNullOrEmpty()) {
                            newSellingPrice = Utility.cleanBalance(bal).toDouble()
                        }
                        val sellingPriceUpdateType = if (state.productEntity?.unitPrice == 0.0) AnalyticsConst.ADDED_NEW else AnalyticsConst.CHANGE_EXISTING

                        AppAnalytics.PropBuilder().apply {
                            put(AnalyticsConst.UPDATE_SELLING_PRICE, newSellingPrice != state.productEntity?.unitPrice)
                            put(AnalyticsConst.SELLING_PRICE_UPDATE_TYPE, sellingPriceUpdateType)
                            put(AnalyticsConst.UPDATED_MINIMUM_STOCK, newMinValue != state.productEntity?.minimumStock)
                            put(AnalyticsConst.LAST_MINIMUM_STOCK_VALUE, state.productEntity?.minimumStock)
                            put(AnalyticsConst.UPDATED_MINIMUM_STOCK_VALUE, newMinValue)
                            put(AnalyticsConst.IS_UPDATED_STOCK_VALUE_GREATER, newMinValue > state.productEntity?.minimumStock ?: 0)
                            put(AnalyticsConst.EDIT_PRODUCT_SOURCE, arguments?.getString(ENTRY_POINT))
                            put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(state.productEntity?.stock))
                        }.also {
//                            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_EDIT_PRODUCT, it)
                        }

                        state.productEntity?.minimumStock = newMinValue
                        state.productEntity?.name = mBinding.productNameET.text.toString()
                        state.productEntity?.measurementName = mBinding.unitName.text.toString()
                        state.productEntity?.unitPrice = newSellingPrice
                        ProductRepository.getInstance(activity).updateProduct(state.productEntity)
                        if (dialog != null) dialog!!.dismiss()
                    } catch (e: Exception) {
                        e.recordException()
                        if (dialog != null) dialog!!.dismiss()
                    }
                }

                mBinding.editMeasurement.setOnClickListener { v: View? ->
                    activity?.apply {
                        val propBuilder = AppAnalytics.PropBuilder()
                        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.EDIT_STOCK)
                        propBuilder.put(AnalyticsConst.STEP, AnalyticsConst.UNIT_OPEN)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
                        StockUnitBottomSheet.instance(state.productEntity!!.bookId, state.productEntity.measurementId)
                                .show(childFragmentManager, "")
                        InputUtils.hideKeyboard(activity)
                    }
                }

            }
        }
    }

    override fun onMeasurementSelected(unitId: String, unitName: String) {
        updateUnitName(unitName)
    }

    override fun addNewMeasurement() {
        activity?.also {
            customMeasurementUnitDialog = CustomMeasurementUnitDialog(it, this)
            customMeasurementUnitDialog?.show()
        }

    }

    override fun addMeasurement(measurement: String) {
        val measurementEntity = MeasurementEntity()
        measurementEntity.measurementName = measurement
        measurementEntity.isDefault = 0
        measurementEntity.measurementId = measurementEntity.generateId()
        measurementEntity.bookId = produtEntity.bookId
        if (measurementEntity.measurementName.length <= UNIT_NAME_LENGTH) {
            InventoryRepository.getInstance(activity).addMeasurementUnit(measurement)
            updateUnitName(measurement)
            customMeasurementUnitDialog?.dismiss()
        } else {
            throw Exception("Unit name should be less than 8")
        }

    }

    private fun updateUnitName(unitName: String) {
        mBinding.unitName.text = unitName
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        InputUtils.hideKeyboard(context)
    }

    private fun closeCalculatorKeyboard() {
        if (requireActivity().isAnimEnabled()) mBinding.keyboardView.clearAnimation()
        mBinding.keyboardView.visibility = View.GONE
        mBinding.keyboardView.cursor = mBinding.cursor
        mBinding.keyboardView.hideCursor()
    }

    private fun changeCalculatorFocusToTrx() {
        try {

            InputUtils.hideKeyboardFrom(requireContext(), mBinding.productNameET)
            clearFocus()
            //binding.noteEt.clearFocus()
            val moveup = AnimationUtils.loadAnimation(requireContext(), R.anim.move_up)
            if (requireActivity().isAnimEnabled()) mBinding.keyboardView.startAnimation(moveup)
            mBinding.keyboardView.visibility = View.VISIBLE
            mBinding.keyboardView.showCursor()
            mBinding.currencySymbol.visibility = View.VISIBLE
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun clearFocus() {
        mBinding.productNameET.clearFocus()
        mBinding.numberStepperLayout.etNumber.clearFocus()
    }

    private fun initKeyboard() {
        mBinding.keyboardView.setResultTv(mBinding.sellingPriceEdit)
        mBinding.keyboardView.cursor = mBinding.cursor
        mBinding.keyboardView.setCurrency(mBinding.currencySymbol)
        mBinding.keyboardView.setExprTv(mBinding.textAmountCalc)
        mBinding.keyboardView.setResultLayout(mBinding.resultLayout)
        mBinding.keyboardView.hideCursor()

    }


    companion object {

        const val TRANSACTION_FLOW = "transaction_flow"
        const val PRODUCT_ID = "product_id"
        const val ENTRY_POINT = "entry_point"


        fun instance(productId: String, isTransactionFlow: Boolean = false): BottomSheetDialogFragment {
            val fragment = EditStockBottomSheetFragment()
            val bundle = Bundle()
            bundle.putBoolean(TRANSACTION_FLOW, isTransactionFlow)
            bundle.putString(PRODUCT_ID, productId)
            fragment.arguments = bundle
            return fragment
        }
    }

}