package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.addcustomer.detail.CustomerActivity
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.expense.NewCashTransactionActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.FragmentProductDashboardTipsEmptyBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.checkout.PaymentCheckoutActivity
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PULSA
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.deeplink.handler.PaymentCheckoutSignalHandler
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm
import com.bukuwarung.utils.RemoteConfigUtils.getSalesOnboardingThreshold
import com.bukuwarung.utils.RemoteConfigUtils.showPreviousTransaction
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject

class BusinessDashboardTipsEmptyFragment : BaseFragment(), Navigator {
    lateinit var binding: FragmentProductDashboardTipsEmptyBinding
    lateinit var viewModel: BusinessDashboardMainViewModel
    private var fromDailyBusiness = true
    private val transCount = 0


    companion object {
        private const val PRODUCT_DASHBOARD_TIPS_EMPTY_FRAGMENT =
            "product_dashboard_tips_empty_fragment"

        fun createIntent(): BusinessDashboardTipsEmptyFragment {
            val fragment = BusinessDashboardTipsEmptyFragment()
            fragment.arguments = Bundle()
            return fragment
        }
    }

    @Inject
    lateinit var neuro: Neuro

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardTipsEmptyBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        setTextTips()
//        binding.cvHome.setSingleClickListener {
//            BusinessDashboardWelcomeActivity.isFromInfo = true
//            startActivity(BusinessDashboardWelcomeActivity.createIntent(requireContext()))
//        }
    }

    override fun subscribeState() {
    }

    fun setTextTips() {
        binding.apply {


            ppopTip.tvHeader.text = getString(R.string.bisa_jualan_pulsa)
            ppopTip.tvSubtext.text = getString(R.string.pembelian_pulsa_atau)
            ppopTip.icInfo.setImageResource(R.drawable.token)

            paymentTip.tvHeader.text = getString(R.string.transaksi_tagih)
            paymentTip.tvSubtext.text = getString(R.string.bisa_untuk_tagih)
            paymentTip.icInfo.setImageResource(R.drawable.fund)

            transactionTip.tvHeader.text =
                getString(R.string.pencatatan_keuangan_bisnis)
            transactionTip.tvSubtext.text =
                getString(R.string.gak_perlu_khawatir)
            transactionTip.icInfo.setImageResource(R.drawable.business_empty_modal)

            utangTip.tvHeader.text =
                getString(R.string.catat_utang_di_bukuwarung)
            utangTip.tvSubtext.text =
                getString(R.string.penting_buat_juragan_catat_utang)
            utangTip.icInfo.setImageResource(R.drawable.business_empty_utang)
        }
       

        val hideModal =
            TransactionRepository.getInstance(context).cashTransactionCountWithDeletedRecords == 0


        val hideUtang =
            TransactionRepository.getInstance(activity).utangTransactionCountWithDeletedRecords == 0

        val hideBrick = !FeaturePrefManager.getInstance().isBrickIntegratedAtleastOnce && RemoteConfigUtils.shouldShowAutoRecordFeature()

        val hidePpobCard = FeaturePrefManager.getInstance().businessDashboardPpobProductShown
        Log.e("hidePpobCard",""+hidePpobCard)
        val hidePaymentCard = FeaturePrefManager.getInstance().businessDashboardPayment



        if (hideUtang) {
            binding.utangDivider.visibility = View.VISIBLE
            binding.utangTip.root.visibility = View.VISIBLE
        }
        if (hideModal) {
            binding.transactionDivider.visibility = View.VISIBLE
            binding.transactionTip.root.visibility = View.VISIBLE
        }

        if (hidePpobCard) {
            binding.ppopDivider.visibility = View.VISIBLE
            binding.ppopTip.root.visibility = View.VISIBLE
        }
        if (hidePaymentCard) {
            binding.paymentDivider.visibility = View.VISIBLE
            binding.paymentTip.root.visibility = View.VISIBLE
        }
        onClickEvent()


    }

    fun onClickEvent() {
    binding.utangTip.btnTryItNow.setOnClickListener{

         val propBuilder = AppAnalytics.PropBuilder()
         propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.BD)
         propBuilder.put(AnalyticsConst.FEATURE, AnalyticsConst.UTANG)
         AppAnalytics.trackEvent(AnalyticsConst.BD_TRY_NEW_FEATURE, propBuilder)


         val intent = Intent(activity, CustomerActivity::class.java)
             intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, AppConst.DEBIT)
             intent.putExtra(CustomerActivity.SHOW_BUSINESS_DASHBOARD, true)

         startActivity(intent)
     }
    binding.transactionTip.btnTryItNow.setOnClickListener{
        val showOldForm = canShowOldTransactionForm()
       val  showLastTransactionType = showPreviousTransaction()
         val propBuilder = AppAnalytics.PropBuilder()
         propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.BD)
         propBuilder.put(AnalyticsConst.FEATURE, AnalyticsConst.TRANSAKSI)
         AppAnalytics.trackEvent(AnalyticsConst.BD_TRY_NEW_FEATURE, propBuilder)

         if (showOldForm) {
             startActivity(NewCashTransactionActivity.createIntent(context))
         } else {
             var trxType: Boolean = transCount < getSalesOnboardingThreshold()
             val intent = NewCashTransactionActivity.createIntent(context)
             if (showLastTransactionType) {
                 trxType = AppConfigManager.getInstance().transactionType
             }
             intent.putExtra(NewCashTransactionActivity.TRX_TYPE, trxType)
             intent.putExtra(NewCashTransactionActivity.SHOW_INTRO, false)
             intent.putExtra(NewCashTransactionActivity.SHOW_BUSINESS_DASHBOARD, true)
             if (fromDailyBusiness) {
                 intent.putExtra(NewCashTransactionActivity.FROM_DAILY_BUSINESS_UPDATE, true)
             }

             startActivity(intent)
             fromDailyBusiness = false
         }
     }
    binding.paymentTip.btnTryItNow.setOnClickListener {
        val sourceLink = SourceLink(
            requireContext(),
            PaymentCheckoutSignalHandler.getPaymentLink(
                paymentType = PaymentHistory.TYPE_PAYMENT_OUT,
                entryPoint = AnalyticsConst.BD
            )
        )
        neuro.route(
            sourceLink, this, {},
            {
                FirebaseCrashlytics.getInstance().recordException(it)
                startActivity(
                    PaymentCheckoutActivity.createIntent(
                        requireContext(),
                        PaymentConst.TYPE_PAYMENT_OUT.toString(),
                        User.getBusinessId(),
                        AnalyticsConst.BD
                    )
                )
            }
        )
    }

        binding.ppopTip.btnTryItNow.setOnClickListener {
            PpobUtils.getPpobCategoryActivityIntent(
                    fragmentManager = childFragmentManager,
                    context = requireContext(), category = CATEGORY_PULSA
            )?.let { startActivity(it) }
        }

    binding.utangTip.tvBody.setOnClickListener {
            startActivity(WebviewActivity.createIntent(requireContext(),
                "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/4404306776847--Fitur-Utang", "Utang"))

        }
    binding.transactionTip.tvBody.setOnClickListener {


            startActivity(WebviewActivity.createIntent(requireContext(),
                "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/4404306777359--Fitur-Transaksi", "Transaksi"))


        }
    binding.ppopTip.tvBody.setOnClickListener {
            startActivity(WebviewActivity.createIntent(requireContext(),
                "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/4404214983055", "PPOB"))

        }
    binding.paymentTip.tvBody.setOnClickListener {
            startActivity(WebviewActivity.createIntent(requireContext(),
                "https://bukuwarungsupport.zendesk.com/hc/id-id/articles/4404448885135", "Payment"))


        }
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }


}