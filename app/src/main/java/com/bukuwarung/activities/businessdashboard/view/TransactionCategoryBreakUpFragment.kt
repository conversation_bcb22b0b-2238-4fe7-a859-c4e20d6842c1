package com.bukuwarung.activities.businessdashboard.view

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.database.entity.CashCategoryEntity
import com.bukuwarung.databinding.FragmentTransactionCategoryBreakupBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.formatter.PercentFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import com.github.mikephil.charting.utils.MPPointF
import dagger.android.support.AndroidSupportInjection
import java.util.*
import javax.inject.Inject
import kotlin.Comparator
import kotlin.collections.ArrayList


class TransactionCategoryBreakUpFragment : BaseFragment() {

    private var isExpanded: Boolean = false
    private lateinit var colors: java.util.ArrayList<Int>
    lateinit var adapterColors: ArrayList<Int>

    @Inject
    lateinit var cashListViewModel: CashListViewModel
    private lateinit var adapter: TransactionCategoryBreakUpAdapter
    private var _binding: FragmentTransactionCategoryBreakupBinding? = null
    private val binding get() = _binding!!

    private var isIncome = false
    private var sdate:String = "2022-01-01"
    private var edate:String = "2022-01-31"
    private var totalTransactions: Int = 0
    private var categories: ArrayList<CashCategoryEntity> = ArrayList()
    private lateinit var colorList: List<TransactionCategoryBreakUpColor>
    private lateinit var btnDetailListener :(isExpanded: Boolean) -> Unit

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    companion object {

        const val IS_INCOME = "is_income"
        const val START_DATE = "start_date"
        const val END_DATE = "end_date"

        fun createInstance(isIncome: Boolean = false,sdate : String,edate: String, btnDetailListener: (isExpanded : Boolean) -> Unit) =
            TransactionCategoryBreakUpFragment().apply {
                val bundle = Bundle()
                bundle.putBoolean(IS_INCOME, isIncome)
                bundle.putString(START_DATE, sdate)
                bundle.putString(END_DATE, edate)
                arguments = bundle
                this.btnDetailListener = btnDetailListener
            }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentTransactionCategoryBreakupBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        isIncome = arguments?.getBoolean(IS_INCOME) ?: false
        sdate = arguments?.getString(START_DATE) ?: sdate
        edate = arguments?.getString(END_DATE) ?: edate
        binding.transactionRecyclerView.layoutManager = LinearLayoutManager(activity)
        return binding.root
    }


    override fun setupView(view: View) {
        if (isIncome) {
            totalTransactions = cashListViewModel.getAllIncomeTrxCountWithProductByDate(sdate,edate)
            binding.tvTotalTransactions.text =
                getString(R.string.total_income_transactions, totalTransactions)
        } else {
            totalTransactions = cashListViewModel.getAllExpenseTrxCountWithProductByDate(sdate,edate)
            binding.tvTotalTransactions.text =
                getString(R.string.total_expense_transactions, totalTransactions)
            binding.tvTotalTransactions.setTextColor(ContextCompat.getColor(requireContext(), R.color.red80))
        }


        if(totalTransactions == 0){
            binding.tvTotalTransactions.setTextColor(ContextCompat.getColor(context!!, R.color.black40))
        }

        if (isIncome) {
            categories = cashListViewModel.getAllIncomeCategories(sdate,edate)
        } else {
            categories = cashListViewModel.getAllExpenseCategories(sdate,edate)
        }


        colorList =
            RemoteConfigUtils.getTransactionCategoryBreakUpColors()

        val chart = binding.chart
        chart.setUsePercentValues(true)
        chart.getDescription().setEnabled(false)
        chart.setExtraOffsets(5f, 10f, 5f, 5f)

        chart.setDragDecelerationFrictionCoef(0.95f)

        chart.setDrawHoleEnabled(true)
        chart.setHoleColor(Color.WHITE)
//        chart.setTransparentCircleColor(Color.WHITE)
        chart.setTransparentCircleAlpha(0)
//        chart.setTransparentCircleAlpha(110)
        chart.setHoleRadius(38f)
//        chart.setTransparentCircleRadius(61f)
        chart.setRotationAngle(0f)
        // enable rotation of the chart by touch
        // enable rotation of the chart by touch
        chart.setRotationEnabled(true)
        chart.setHighlightPerTapEnabled(true)

        chart.animateY(1400, Easing.EasingOption.EaseInOutQuad)
        // chart.spin(2000, 0, 360);

        // chart.spin(2000, 0, 360);
        val l = chart.legend
        l.isEnabled = false

        setData(colorList)

        adapter = TransactionCategoryBreakUpAdapter(context!!, isIncome, totalTransactions)
        binding.transactionRecyclerView.adapter = adapter
        adapterColors = colors.clone() as ArrayList<Int>
        if(totalTransactions == 0) adapterColors.removeAt(0)
        if(categories.size>3) {
            adapter.updateData(categories.subList(0,3), adapterColors)
        } else{
            adapter.updateData(categories, adapterColors)
        }

    }

    private fun setData(colorList: List<TransactionCategoryBreakUpColor>) {
        val entries = ArrayList<PieEntry>()

        for (i in 0 until categories.size) {
            categories.get(i).frequency = if (isIncome == true) cashListViewModel.getIncomeTrxCountWithIdByDate(
                categories.get(i).cashCategoryId,sdate,edate
            )
            else cashListViewModel.getExpenseTrxCountWithIdByDate(categories.get(i).cashCategoryId,sdate,edate)
            categories.get(i).balance = Math.abs(cashListViewModel.getCategoryAmountByDate(
                categories.get(i).cashCategoryId,sdate,edate
            ).toDouble())
        }

        Collections.sort(categories, object : Comparator<CashCategoryEntity> {
            override fun compare(one: CashCategoryEntity, other: CashCategoryEntity): Int {
                return other.frequency.compareTo(one.frequency)
            }
        })

        if(totalTransactions == 0 || categories.size==0){
            entries.add(
                PieEntry(
                    "100".toFloat(),
                    "",
                    resources.getDrawable(R.drawable.star)
                )
            )
        }
        else{
            for (i in 0 until categories.size) {
                entries.add(
                    PieEntry(
                        if (isIncome == true) cashListViewModel.getIncomeTrxCountWithIdByDate(
                            categories.get(i).cashCategoryId,sdate,edate
                        )
                            .toFloat() else cashListViewModel.getExpenseTrxCountWithIdByDate(categories.get(i).cashCategoryId,sdate,edate)
                            .toFloat(),
//                    categories.get(i).name
                        "",
                        resources.getDrawable(R.drawable.star)
                    )
                )
            }
        }

        val dataSet = PieDataSet(entries, "Transaction Category Summary")
        dataSet.setDrawIcons(false)
        dataSet.sliceSpace = 3f
        dataSet.iconsOffset = MPPointF(0f, 40f)
        dataSet.selectionShift = 5f

        // add a lot of colors
        colors = ArrayList<Int>()

        if(totalTransactions == 0 || categories.size==0){
            colors.add(ContextCompat.getColor(context!!, R.color.black40))
        }

        for (c in colorList) colors.add(Color.parseColor(c.color))
        for (c in ColorTemplate.VORDIPLOM_COLORS) colors.add(c)
        for (c in ColorTemplate.JOYFUL_COLORS) colors.add(c)
        for (c in ColorTemplate.COLORFUL_COLORS) colors.add(c)
        for (c in ColorTemplate.LIBERTY_COLORS) colors.add(c)
        for (c in ColorTemplate.PASTEL_COLORS) colors.add(c)
        colors.add(ColorTemplate.getHoloBlue())
        dataSet.colors = colors
        //dataSet.setSelectionShift(0f);
        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())
        data.setValueTextSize(11f)
        data.setValueTextColor(Color.WHITE)
        data.setDrawValues(false)
//        data.setValueTypeface(tfLight)
        binding.chart.setData(data)

        // undo all highlights
        binding.chart.highlightValues(null)
        binding.chart.invalidate()
    }

    override fun subscribeState() {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        // ViewBinding cleanup
        _binding = null
    }

    fun btnDetailClick() {
        if(isExpanded){
            if(categories.size>3) {
                adapter.updateData(categories.subList(0,3), adapterColors)
            } else{
                adapter.updateData(categories, adapterColors)
            }
            binding.seeAll.text = getString(R.string.lihat_semua)
            isExpanded = false
        }
        else{
            adapter.updateData(categories, adapterColors)
            binding.seeAll.text = getString(R.string.show_less)
            isExpanded = true
        }
        btnDetailListener(isExpanded)

    }

}