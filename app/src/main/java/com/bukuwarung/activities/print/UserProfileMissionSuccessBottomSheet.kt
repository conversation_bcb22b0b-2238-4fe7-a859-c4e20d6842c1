package com.bukuwarung.activities.print

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.payment.PaymentTabViewModel
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.databinding.UserProfileMissionSuccessBottomSheetBinding
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.showView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject


class UserProfileMissionSuccessBottomSheet : BaseBottomSheetDialogFragment() {
    companion object {
        const val TAG = "UserProfileMissionSuccessBS"
        private const val FROM_LAINNYA = "from_lainnya"
        private const val ENTRY_POINT = "entry_point"

        fun createInstance(isFromLainnyaTab: Boolean = false, entryPoint: String) =
            UserProfileMissionSuccessBottomSheet().apply {
                arguments = Bundle().apply {
                    putBoolean(FROM_LAINNYA, isFromLainnyaTab)
                    putString(ENTRY_POINT, entryPoint)
                }
            }
    }

    enum class FROM {
        FROM_EDIT_PROFILE,
        NOTES_MISSION
    }

    private val isFromLainnyaTab by lazy { arguments?.getBoolean(FROM_LAINNYA).isTrue }
    private val entryPoint by lazy { arguments?.getString(ENTRY_POINT).orEmpty() }
    private var callback: Callback? = null

    @Inject
    lateinit var viewModel: PaymentTabViewModel

    interface Callback {
        fun seeNote(entryPoint: String)
    }

    private lateinit var binding: UserProfileMissionSuccessBottomSheetBinding

    override fun onAttach(context: Context) {
        super.onAttach(context)
        AndroidSupportInjection.inject(this)
        parentFragment?.let { callback = it as? Callback }
        if (context is Callback) callback = context
    }

    private fun dismissDialogAndCoachmark() {
        dismiss()
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val displayMetrics = requireActivity().resources.displayMetrics
            val height = displayMetrics.heightPixels

            val maxHeight = (height * 0.88).toInt()
            val mBehavior: BottomSheetBehavior<*> = BottomSheetBehavior.from(requireView().parent as View)
            mBehavior.peekHeight = maxHeight
        }
        return dialog
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = UserProfileMissionSuccessBottomSheetBinding.inflate(inflater, container, false)

        if (isFromLainnyaTab) {
            binding.continueBtn.hideView()
            binding.cancelBtn.hideView()
            binding.understandBtn.showView()
            binding.subtitleTxt.hideView()
            binding.titleTxt.text =
                getString(R.string.congratulations_you_have_completed_your_profile)
            binding.understandBtn.setOnClickListener {
                dismissDialogAndCoachmark()
            }
        }

        binding.continueBtn.setOnClickListener {
            callback?.seeNote(entryPoint)
            dismissDialogAndCoachmark()
        }
        binding.cancelBtn.setOnClickListener {
            dismissDialogAndCoachmark()
        }

        return binding.root
    }

}