package com.bukuwarung.activities.expense

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.ProductAdapter
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.activities.expense.adapter.viewholder.ProductDividerViewHolder
import com.bukuwarung.activities.expense.adapter.viewholder.ProductViewHolderEvent.*
import com.bukuwarung.activities.inventory.detail.EditStockActivity
import com.bukuwarung.activities.inventory.detail.EditStockBottomSheetFragment
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.ActivityProductListBinding
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.inventory.ui.product.AddProductFragment
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TUTOR_CREATE_PRODUCT
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TUTOR_PRODUCT_QUANTITY
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject


class ProductListActivity : BaseActivity(), OnboardingWidget.OnboardingWidgetListener {
    companion object {
        const val SELECTED_PRODUCTS = "SELECTED_PRODUCTS"
        const val EXISTING_PRODUCTS = "EXISTING_PRODUCTS"
        const val IS_EDIT = "IS_EDIT"
        const val IS_EXPENSE = "IS_EXPENSE"

        fun createIntent(context: Context) = Intent(context, ProductListActivity::class.java)
    }

    @Inject
    lateinit var viewModelFactory: ProductListViewModelFactory
    private val viewModel: ProductListViewModel by viewModels { viewModelFactory }

    private lateinit var binding: ActivityProductListBinding

    private var onboardingWidget: OnboardingWidget? = null
    private var isShowingProductNameTutor = false

    private var productAdapter: ProductAdapter? = null
    private var isExpense: Boolean = false
    private var showFirstProductDailogue = false

    private var from: String? = null

    private var isStockToggleEnabled = RemoteConfigUtils.isStockToggleEnabled()

    override fun setViewBinding() {
        binding = ActivityProductListBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setUpToolbarWithHomeUp(binding.tb)
        showFirstProductDailogue = true
        InputUtils.hideKeyBoardWithCheck(this)
        isExpense = intent.getBooleanExtra(IS_EXPENSE, false)
        productAdapter = ProductAdapter(isExpense) { dataHolder, adapterEvent ->
            when (adapterEvent) {
                Selection -> viewModel.onEventReceived(ProductListViewModel.Event.ToggleProductChecked(dataHolder))
                Edit -> {
                    openEditScreen(dataHolder.productEntity?.productId)
                }
                Delete -> {
                    GenericConfirmationDialog.create(this) {
                        titleRes = R.string.delete_confirmation_title
                        bodyRes = R.string.delete_confirmation_body
                        btnLeftRes = R.string.delete
                        btnRightRes = R.string.cancel
                        leftBtnCallback = {
                            viewModel.onEventReceived(ProductListViewModel.Event.CheckProductIsDeletable(dataHolder))
                        }
                    }.show()
                }
            }

        }
        binding.apply {
            rvProduct.apply {
                layoutManager = LinearLayoutManager(this@ProductListActivity)
                adapter = productAdapter
            }

            if (isStockToggleEnabled) {
                btnAddProductSearch.setOnClickListener {
                    val intent = EditStockActivity.getNewIntent(
                        this@ProductListActivity,
                        "", User.getBusinessId(),
                        isTransactionalAddFlow = true, productName = etSearch.text.toString()
                    )
                    startActivityForResult(intent, EditStockActivity.PRODUCT_ADDED_SUCCESS)
                }
            }

            btnAddProduct.setOnClickListener {
                if (etSearch.text.toString().isBlank()) return@setOnClickListener

                InputUtils.hideKeyBoardWithCheck(this@ProductListActivity)

                if (isStockToggleEnabled) {
                    val intent = EditStockActivity.getNewIntent(
                        this@ProductListActivity,
                        "", User.getBusinessId(),
                        isTransactionalAddFlow = true, productName = etSearch.text.toString()
                    )
                    startActivityForResult(intent, EditStockActivity.PRODUCT_ADDED_SUCCESS)
                } else if (!isExpense) {
                    supportFragmentManager.beginTransaction().add(
                        R.id.container,
                        AddProductFragment.instance(
                            User.getBusinessId(),
                            etSearch.text.toString(),
                            isTransactionFlow = true
                        )
                    ).commit()

                } else {
                    viewModel.onEventReceived(ProductListViewModel.Event.CreateNewProduct(etSearch.text.toString()))
                }
            }

            etSearch.addTextChangedListener {
                viewModel.onEventReceived(
                    ProductListViewModel.Event.FilterProduct(
                        it?.toString()
                            ?: ""
                    )
                )
            }

            btnSave.setOnClickListener {
                currentFocus?.let {
                    it.clearFocus()
                    etSearch.requestFocus()
                }
                viewModel.onEventReceived(ProductListViewModel.Event.FinalizeProductChoice)
            }
        }

        val existingProducts = (intent.getParcelableArrayExtra(EXISTING_PRODUCTS))
        val isFromEdit = intent.getBooleanExtra(IS_EDIT, false)
        viewModel.onEventReceived(ProductListViewModel.Event.OnCreateView(existingProducts, isFromEdit, isExpense))
        viewModel.onEventReceived(ProductListViewModel.Event.ShowProductNameTutorial)

        binding.tvProductInfo.setOnClickListener {
            if (binding.tvProductInfo.isVisible) {
                binding.tvProductInfo.visibility = View.GONE
            }
        }
        binding.layoutNoProduct.tvBlankScreenMessage.text =
            SpannableStringBuilder(getString(R.string.empty_product_message)).colorText(
                "Tambah Barang",
                getColorCompat(R.color.colorAccent),
                true
            )
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                ProductListViewModel.State.NewProductCreated -> {
                    binding.btnAddProductGrp.visibility = View.GONE
                    if (isStockToggleEnabled) {
                        binding.btnAddProductSearch.visibility = View.VISIBLE
                    }
                    binding.etSearch.text?.clear()
                }
                ProductListViewModel.State.ProductUpdated -> binding.etSearch.text?.clear()
                is ProductListViewModel.State.ProductDeletable -> {
                    if (it.isNotDeletable) {
                        Toast.makeText(this@ProductListActivity, getString(R.string.product_already_used), Toast.LENGTH_SHORT).show()
                    } else {
                        viewModel.onEventReceived(ProductListViewModel.Event.DeleteProduct(it.dataHolder))
                    }
                }
                ProductListViewModel.State.ProductDeleted -> binding.etSearch.text?.clear()
                is ProductListViewModel.State.OnFinalizeProductChoice -> {
                    InputUtils.hideKeyBoardWithCheck(this)

                    val resultIntent = Intent().apply {
                        putExtra(SELECTED_PRODUCTS, it.data)
                    }
                    setResult(RESULT_OK, resultIntent)
                    finish()
                }
                ProductListViewModel.State.TutorCreateProduct -> {
                }
                ProductListViewModel.State.ShowNoSellingPriceCoachMark -> {
                    if (!isExpense) showZeroSellingPriceCoachMark()
                }
                is ProductListViewModel.State.ShowEditScreen -> openEditScreen(it.productId)
            }
        }

        viewModel.liveDataMerger.observe(this, Observer {
            binding.rvProduct.post { productAdapter?.setData(it) }
            val filtered = it.filterIsInstance<ProductDataHolder>()
            val query = binding.etSearch.text.toString()
            if (query.isNotEmpty()) {
                val spannableStringBuilder = SpannableStringBuilder("${getString(R.string.add_text)} ${"\""}$query${"\""} ${getString(R.string.to_item_list)}")
                spannableStringBuilder.setSpan(StyleSpan(Typeface.BOLD), 7, query.length + 9, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                binding.searchProductNameText.text = spannableStringBuilder
            }

            binding.layoutNoProduct.root.visibility = (query.isBlank() && filtered.isEmpty()).asVisibility()
            binding.btnAddProductGrp.visibility = (query.isNotEmpty()).asVisibility()
            if (isStockToggleEnabled) {
                binding.btnAddProductSearch.visibility = (query.isEmpty()).asVisibility()
            }
            if (it.isEmpty() && !isExpense && showFirstProductDailogue) {
                showFirstProductDailogue = false

                if (isStockToggleEnabled) {
                    val intent = EditStockActivity.getNewIntent(
                        this, "", User.getBusinessId(),
                        isTransactionalAddFlow = true
                    )
                    startActivityForResult(intent, EditStockActivity.PRODUCT_ADDED_SUCCESS)
                } else {
                    supportFragmentManager.beginTransaction().add(
                        R.id.container,
                        AddProductFragment.instance(
                            User.getBusinessId(), binding.etSearch.text.toString(),
                            true
                        )
                    ).commit()
                }
            } else
                showFirstProductDailogue = false

            /**
             * check if productName coachmark is showing
             */
            if (onboardingWidget != null) {
                if (onboardingWidget!!.getUsageId() == TUTOR_CREATE_PRODUCT && !isShowingProductNameTutor) {
                    showProductCoachMark()
                }
            } else {
                showProductCoachMark()
            }

            binding.btnSave.isEnabled = filtered.any { product -> product.isChecked }
        })
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onBackPressed() {
        if (onboardingWidget?.isShown.isTrue) {
            onboardingWidget?.dismiss()
        } else {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }
    }

    override fun onOnboardingDismiss(id: String?, body: String, isFromButton: Boolean, isFromCloseButton: Boolean, isFromOutside: Boolean) {
        id?.let {
            when (id) {
                OnboardingPrefManager.TUTOR_PRODUCT_STOCK_ZERO_SELLING_PRICE -> {
                    viewModel.onEventReceived(ProductListViewModel.Event.ProductSellingPriceTutorialClicked(true))
                }
                else -> {
                    if (it == TUTOR_CREATE_PRODUCT) {
                        isShowingProductNameTutor = false
                        showProductCoachMark()
                    }
                    OnboardingPrefManager.getInstance().setHasFinishedForId(it)
                }
            }
        }
    }

    private fun showProductCoachMark() {
        if (!OnboardingPrefManager.getInstance().getHasFinishedForId(TUTOR_PRODUCT_QUANTITY)) {
            lifecycleScope.launch {
                delay(250)

                binding.rvProduct.post {
                    binding.rvProduct.findViewHolderForLayoutPosition(0)?.let { viewHolder ->
                        /**
                         * will not show the coachmark if there's no product checked
                         */
                        if (viewHolder is ProductDividerViewHolder) return@let

                        onboardingWidget = OnboardingWidget.createInstance(
                            this@ProductListActivity, this@ProductListActivity,
                            TUTOR_PRODUCT_QUANTITY, viewHolder.itemView, R.drawable.onboarding_attention, "",
                            getString(R.string.onboarding_product_quantity), "",
                            FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 2, 2
                        )

                    }
                }
            }
        }
    }

    fun onProductCreated(productEntity: ProductEntity, from: String?) {
        viewModel.onEventReceived(ProductListViewModel.Event.CheckNewProduct(productEntity, from))
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        id?.also {
            when (id) {
                OnboardingPrefManager.TUTOR_PRODUCT_STOCK_ZERO_SELLING_PRICE ->
                    viewModel.onEventReceived(ProductListViewModel.Event.ProductSellingPriceTutorialClicked(false))
            }
        }
    }

    private fun openEditScreen(productId: String?) {
        if (isStockToggleEnabled) {
            productId?.let {
                val intent = EditStockActivity.getNewIntent(
                    this, it, "", true
                )
                startActivityForResult(intent, EditStockActivity.PRODUCT_ADDED_SUCCESS)
            }
        } else {
            val bottomSheetFragment = EditStockBottomSheetFragment()
            val bundle = Bundle()
            bundle.putString(EditStockBottomSheetFragment.PRODUCT_ID, productId)
            bundle.putBoolean(EditStockBottomSheetFragment.TRANSACTION_FLOW, true)
            bundle.putString(EditStockBottomSheetFragment.ENTRY_POINT, AnalyticsConst.VIA_TRANSAKSI_FORM)
            bottomSheetFragment.arguments = bundle
            bottomSheetFragment.show(supportFragmentManager, bottomSheetFragment.tag)
        }
    }

    private fun showZeroSellingPriceCoachMark() {
        lifecycleScope.launch {
            delay(250L)
            binding.rvProduct.post {
                if (!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTOR_PRODUCT_STOCK_ZERO_SELLING_PRICE)) {
                    binding.rvProduct.post {
                        binding.rvProduct.findViewHolderForLayoutPosition(0)?.let { viewHolder ->
                            onboardingWidget = OnboardingWidget.createInstance(
                                this@ProductListActivity, this@ProductListActivity,
                                OnboardingPrefManager.TUTOR_PRODUCT_STOCK_ZERO_SELLING_PRICE, viewHolder.itemView, R.drawable.onboarding_announce, "",
                                getString(R.string.onboarding_product_stock_zero_selling_price), getString(R.string.try_feature),
                                FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 1, 2
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == EditStockActivity.PRODUCT_ADDED_SUCCESS && resultCode == RESULT_OK) {
            val productId = data?.getStringExtra("PRODUCT_ID") ?: return
            viewModel.onEventReceived(ProductListViewModel.Event.CheckNewProductId(productId))
        }
    }
}