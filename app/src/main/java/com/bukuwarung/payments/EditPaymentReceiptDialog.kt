package com.bukuwarung.payments

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.activityViewModels
import com.bukuwarung.R
import com.bukuwarung.databinding.DialogEditPaymentReceiptBinding
import com.bukuwarung.dialogs.base.BaseDialogFragment
import com.bukuwarung.payments.data.model.PaymentReceiptDto
import com.bukuwarung.payments.viewmodels.PaymentHistoryDetailViewModel
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import dagger.android.support.AndroidSupportInjection

class EditPaymentReceiptDialog : BaseDialogFragment() {

    private var dialogEditPaymentReceiptBinding: DialogEditPaymentReceiptBinding? = null
    private val binding get() = dialogEditPaymentReceiptBinding!!

    private val viewModel: PaymentHistoryDetailViewModel by activityViewModels()
    override fun onAttach(context: Context) {
        AndroidSupportInjection.inject(this)
        super.onAttach(context)
    }

    companion object {
        private const val SERVICE_FEE = "service_fee"
        private const val TOTAL_TRANSACTION = "total_transaction"
        private const val TRANSACTION_NOTE = "transaction_note"
        fun newInstance(totalTransaction: Double?, serviceFee: Double?, transactionNote: String?) = EditPaymentReceiptDialog().apply {
            arguments = Bundle().apply {
                putDouble(SERVICE_FEE, serviceFee.orNil)
                putDouble(TOTAL_TRANSACTION, totalTransaction.orNil)
                putString(TRANSACTION_NOTE, transactionNote)
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialogEditPaymentReceiptBinding = DialogEditPaymentReceiptBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            dialog?.window?.statusBarColor = requireContext().getColorCompat(R.color.blue_60)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.viewState.observe(this) {
            populatePaymentReceipt(it)
            binding.etInputNominal.setText(Utility.formatAmount(it.agentFeeInfo?.amount))
        }
        with(binding) {
            includeToolBar.ivHelp.hideView()
            includeToolBar.tvHelp.hideView()
            includeToolBar.tbPpob.navigationIcon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_arrow_back)
            includeToolBar.tbPpob.setNavigationOnClickListener {
                dismiss()
            }
            val paymentIn = viewModel.isPaymentIn()
            if (paymentIn || viewModel.isQrisPaymentIn()) {
                tvActualMoneyMessage2.showView()
                includeToolBar.toolBarLabel.text = getString(R.string.change_bill_receipt, getString(R.string.payment_label_payment_in))
            } else {
                tvActualMoneyMessage1.hideView()
                includeToolBar.toolBarLabel.text = getString(R.string.change_bill_receipt, getString(R.string.payment_label_payment_out))
            }
            btSaveEdit.setOnClickListener {
                val currency = getString(R.string.currency)
                val amountString = if (etInputNominal.text.toString() == currency) getString(R.string.zero_amount) else etInputNominal.text.toString()
                val amount = try {
                    (Utility.cleanBalance(amountString.substring(currency.length))).toDoubleOrNull()
                } catch (e: Exception) {
                    (Utility.cleanBalance(amountString.substring(Utility.getCurrency().length))).toDoubleOrNull()
                }
                viewModel.updateAgentFee(amount.orNil, etWriteNotes.text.toString())
                dismiss()
            }
            etWriteNotes.textHTML(arguments?.getString(TRANSACTION_NOTE).orDefault("-"))
            etWriteNotes.addTextChangedListener {
                binding.paymentReceipt.setNotePreview(
                    getString(R.string.label_payment_in_note, etWriteNotes.text.toString())
                )
            }

            etInputNominal.isCursorVisible = false
            etInputNominal.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    etInputNominal.post {
                        etInputNominal.setSelection(etInputNominal.length())
                        etInputNominal.isCursorVisible = true
                    }
                } else etInputNominal.isCursorVisible = false
            }
            etInputNominal.setOnClickListener {
                etInputNominal.setSelection(etInputNominal.length())
            }
            etInputNominal.doOnTextChanged { text, _, _, _ ->
                if (paymentIn || viewModel.isQrisPaymentIn()) {
                    if (Utility.getAmountInDouble(text.toString()) > arguments?.getDouble(TOTAL_TRANSACTION) ?: 0.0) {
                        NotificationUtils.alertToast(getString(R.string.service_fee_more_than_transaksi_amount))
                        etInputNominal.setText(Utility.formatAmount(Utility.getAmountInDouble(text.toString().substring(0, text.toString().length - 1))))
                        etInputNominal.setSelection(etInputNominal.text.toString().length)
                    }
                }
            }
            val amt = arguments?.getDouble(TOTAL_TRANSACTION) ?: 0.0
            etInputNominal.addTextChangedListener {
                setAmount(amt, paymentIn)
                binding.paymentReceipt.setServiceFee(etInputNominal.getNumberValue().toDouble())
            }
            setAmount(amt, paymentIn)
        }
    }

    private fun setAmount(amt: Double, paymentIn: Boolean) {
        with(binding) {
            if (paymentIn || viewModel.isQrisPaymentIn()) {
                val paymentInAmount = amt - etInputNominal.getNumberValue()
                tvNominal.text = Utility.formatAmount(paymentInAmount)
                tvTotalTransaction.text = Utility.formatAmount(amt)
                binding.paymentReceipt.setNominalAmount(Utility.formatAmount(paymentInAmount))
                orderInvoice.updatePaymentInAmounts(
                    billAmount = paymentInAmount,
                    serviceFee = etInputNominal.getNumberValue().toDouble()
                )
            } else {
                val paymentOutAmount = amt + etInputNominal.getNumberValue()
                tvNominal.text = Utility.formatAmount(amt)
                tvTotalTransaction.text = Utility.formatAmount(paymentOutAmount)
                binding.paymentReceipt.setNominalAmount(Utility.formatAmount(paymentOutAmount))
                orderInvoice.updatePaymentOutAmounts(
                    totalAmount = paymentOutAmount,
                    serviceFee = etInputNominal.getNumberValue().toDouble()
                )
            }
        }
    }

    private fun populatePaymentReceipt(state: PaymentHistoryDetailViewModel.ViewState) {
        val sellingPrice = when {
            state.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> state.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            state.ppobItem?.sellingPrice.orNil > 0.0 -> state.ppobItem?.sellingPrice
            viewModel.isPaymentOut() -> state.amount.minus(state.ppobItem?.discountedFee.orNil).plus(state.loyaltyDiscount).plus(state.subscriptionDiscount)
            else -> state.amount
        }
        if (RemoteConfigUtils.useNewOrderInvoice()) {
            binding.orderInvoice.apply {
                makeInvoice(
                    bookEntity = state.bookEntity,
                    orderResponse = viewModel.orderResponse,
                    paymentType = viewModel.paymentType,
                    customerEntity = viewModel.customer,
                    userProfileEntity = viewModel.getUserProfile(User.getUserId())
                )
                showView()
            }
            binding.paymentReceipt.hideView()
        } else {
            binding.paymentReceipt.setup {
                setBook { state.bookEntity }
                setCustomer { viewModel.customer }
                setReceiverBank { state.receiverBank }
                setDto(
                    {
                        PaymentReceiptDto(
                            paymentChannel = state.paymentChannel,
                            transactionId = state.transactionId,
                            completedStatusDate = state.completedStatusDate,
                            isPPOB = !viewModel.isPaymentIn() && !viewModel.isPaymentOut() && !viewModel.isQrisPaymentIn(),
                            amount = sellingPrice ?: 0.0,
                            paymentFreeChargeStatus = AppConfigManager.getInstance().paymentFreeChargeStatus,
                            isPaymentIn = viewModel.isPaymentIn(),
                            isQrisPaymentIn = viewModel.isQrisPaymentIn(),
                            note = state.transactionNote,
                            ppobCategory = state.ppobCategory,
                            ppobItem = state.ppobItem,
                            agentFeeInfo = state.agentFeeInfo,
                            categoryCode = state.ppobItem?.beneficiary?.code
                        )
                    }, viewModel.getUserProfile(User.getUserId())
                )
            }
            binding.orderInvoice.hideView()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dialogEditPaymentReceiptBinding = null
    }

}