package com.bukuwarung.payments.ppob.base.view

import android.content.Context
import android.os.Bundle
import com.bukuwarung.databinding.PpobDialogBinding
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.utils.setSingleClickListener


class PpobDialog(
    context: Context,
    private val action: () -> Unit,
    private val onDismissAction: () -> Unit,
    private val title: String,
    private val image: Int,
    private val body: String,
    private val buttonText: String
) : BaseDialog(context, BaseDialogType.POPUP) {

    private val binding by lazy {
        PpobDialogBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    init {
        this.setUseFullWidth(false)
        this.setCancellable(false)
    }

    override fun getResId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        setupView()
    }

    private fun setupView() {
        with(binding) {
            ivCross.setSingleClickListener {
                onDismissAction()
                dismiss()
            }
            tvHeading.text = title
            tvBody.text = body
            ivLogo.setImageResource(image)
            bbUnderstand.text = buttonText
            bbUnderstand.setSingleClickListener {
                action()
                dismiss()
            }
        }
    }

}