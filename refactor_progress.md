# CashTransactionDetailActivity View Binding Refactor Progress

## Task: Convert all synthetic imports to view binding

### Files to modify:
- `app/src/main/java/com/bukuwarung/activities/expense/detail/CashTransactionDetailActivity.kt`

### Synthetic imports to remove:
- `kotlinx.android.synthetic.main.cash_receipt_layout.*`
- `kotlinx.android.synthetic.main.cash_receipt_layout.view.*`
- `kotlinx.android.synthetic.main.layout_product_for_receipt.view.*`
- `kotlinx.android.synthetic.main.transaction_receipt_header.*`
- `kotlinx.android.synthetic.main.transaction_receipt_layout.*`

### View IDs to convert (from build errors):

#### Main Activity Layout (activity_cash_transaction_detail.xml):
- `toolBar` → `binding.toolBar`
- `app_bar` → `binding.appBar`
- `transaction_summary` → `binding.transactionSummary`
- `btn_invoice_preference` → `binding.btnInvoicePreference`
- `content_root` → `binding.contentRoot`
- `tvOffline` → `binding.tvOffline`
- `closeBtn` → `binding.closeBtn`
- `copy_img` → `binding.copyImg`
- `trx_no_txt` → `binding.trxNoTxt`
- `closebtnCross` → `binding.closebtnCross`
- `btn_create_payment` → `binding.btnCreatePayment`
- `reload_btn` → `binding.reloadBtn`
- `unpaid_payment_query_group` → `binding.unpaidPaymentQueryGroup`
- `payment_others_group` → `binding.paymentOthersGroup`
- `payment_detail_group` → `binding.paymentDetailGroup`
- `error_state_group` → `binding.errorStateGroup`
- `loading_state_group` → `binding.loadingStateGroup`
- `payment_layout` → `binding.paymentLayout`
- `payment_info` → `binding.paymentInfo`
- `editBtn` → `binding.editBtn`
- `deleteBtn` → `binding.deleteBtn`
- `payment_id_txt` → `binding.paymentIdTxt`
- `expired_title_txt` → `binding.expiredTitleTxt`
- `share_payment_btn` → `binding.sharePaymentBtn`
- `copy_img2` → `binding.copyImg2`
- `payment_account_txt` → `binding.paymentAccountTxt`
- `bank_account_name_txt` → `binding.bankAccountNameTxt`
- `admin_fee_txt` → `binding.adminFeeTxt`
- `admin_fee_title_txt` → `binding.adminFeeTitleTxt`
- `link_et` → `binding.linkEt`
- `layout_payment_details` → `binding.layoutPaymentDetails`
- `trx_no_title_txt` → `binding.trxNoTitleTxt`
- `bank_txt` → `binding.bankTxt`
- `product_name_txt` → `binding.productNameTxt`
- `loadingContainer` → `binding.loadingContainer`
- `notFoundContainer` → `binding.notFoundContainer`
- `mainContainer` → `binding.mainContainer`
- `ll_info` → `binding.llInfo`
- `top_divider` → `binding.topDivider`
- `img_transaksi` → `binding.imgTransaksi`
- `tv_upload_photo` → `binding.tvUploadPhoto`
- `btn_success` → `binding.btnSuccess`
- `btn_mark_paid_payment` → `binding.btnMarkPaidPayment`
- `mark_paid_payment_btn2` → `binding.markPaidPaymentBtn2`
- `bg_margin` → `binding.bgMargin`
- `tv_is_profit` → `binding.tvIsProfit`
- `tv_margin_nominal` → `binding.tvMarginNominal`
- `tv_base_price_nominal` → `binding.tvBasePriceNominal`
- `tv_income_nominal` → `binding.tvIncomeNominal`
- `ask_payment_title_txt` → `binding.askPaymentTitleTxt`
- `ask_payment_subtitle_txt` → `binding.askPaymentSubtitleTxt`
- `layout_redirect_to_payments` → `binding.layoutRedirectToPayments`

#### Included Layout (bottom_share_invoice_layout.xml):
- `bt_print_invoice` → `binding.layoutBottomContainer.btPrintInvoice`
- `bt_general_share` → `binding.layoutBottomContainer.btGeneralShare`
- `bt_share_whatsapp` → `binding.layoutBottomContainer.btShareWhatsapp`
- `tv_open_form` → `binding.layoutBottomContainer.tvOpenForm`

#### Cash Receipt Layout Views:
- `cash_receipt` → `binding.cashReceipt`
- `cash_receipt_old` → `binding.cashReceiptOld`

#### Transaction Receipt Header Views (from synthetic imports):
- `tvWarungName` → Need to access through cash receipt views
- `text` → Need to access through cash receipt views
- `setTextColor` → Need to access through cash receipt views

### Status:
- [ ] Remove synthetic imports
- [ ] Convert all view references to binding
- [ ] Test build
- [ ] Verify functionality

### Notes:
- Some views are accessed through included layouts, need to use proper binding hierarchy
- Cash receipt views have their own internal structure that may need special handling
- Some references like `text` and `setTextColor` are property accesses that need to be converted to proper method calls
